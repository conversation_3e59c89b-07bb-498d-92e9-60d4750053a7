/**
 * 统一数据管理器 - 负责所有组件的数据获取、缓存和实时更新
 */
class DataManager {
    constructor(taskId = null) {
        this.taskId = taskId;  // 改为taskId支持HTTP轮询架构
        this.projectId = taskId; // 保留兼容性
        this.data = new Map();
        this.subscribers = new Map();
        this.httpClient = new HttpClient();
        this.cache = new Map();
        this.cacheTimeout = new Map();

        // HTTP轮询相关
        this.pollingInterval = null;
        this.pollingActive = false;

        // HTTP轮询架构：只有在有taskId时才启动轮询
        if (taskId && taskId !== 'no-task') {
            this.startPolling();
        } else {
            console.log('No task ID provided, polling disabled. User can create workspace.');
        }
    }
    
    /**
     * 统一的数据获取方法
     * @param {string} dataType - 数据类型
     * @param {object} params - 请求参数
     * @param {boolean} useCache - 是否使用缓存
     * @returns {Promise} 数据Promise
     */
    async fetchData(dataType, params = {}, useCache = true) {
        const cacheKey = `${dataType}_${JSON.stringify(params)}`;

        // 检查缓存
        if (useCache && this.cache.has(cacheKey)) {
            const cachedData = this.cache.get(cacheKey);
            this.setData(cacheKey, cachedData);
            return cachedData;
        }

        // 获取数据类型配置
        const config = this.getDataTypeConfig(dataType);

        // 如果有Mock数据且在开发模式下，优先使用Mock数据
        if (config.mockData && (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1')) {
            console.log(`Using mock data for ${dataType}`);
            const data = config.mockData;
            this.setData(dataType, data);

            // 设置缓存
            if (useCache && config.cache) {
                this.setCache(cacheKey, data, config.cacheTTL || 60000);
            }

            return data;
        }

        // HTTP轮询架构：数据通过轮询获取，不使用单独的fetchData
        console.warn(`fetchData called for ${dataType} - HTTP polling architecture gets data via polling`);

        // 检查是否已有数据（来自轮询）
        const existingData = this.getData(dataType);
        if (existingData) {
            return existingData;
        }

        // 如果有Mock数据，使用Mock数据
        if (config.mockData) {
            console.log(`Using mock data for ${dataType}`);
            const data = config.mockData;
            this.setData(dataType, data);
            return data;
        }

        // 返回默认空数据
        const defaultData = {};
        this.setData(dataType, defaultData);
        return defaultData;
    }
    
    /**
     * 统一的数据设置方法
     * @param {string} key - 数据键
     * @param {any} value - 数据值
     */
    setData(key, value) {
        const oldValue = this.data.get(key);
        this.data.set(key, value);
        
        // 通知所有订阅者
        const subscribers = this.subscribers.get(key) || [];
        subscribers.forEach(callback => {
            try {
                callback(value, oldValue);
            } catch (error) {
                console.error('Subscriber error:', error);
            }
        });
        
        console.log(`Data updated: ${key}`, value);
    }
    
    /**
     * 统一的数据订阅方法
     * @param {string} key - 数据键
     * @param {function} callback - 回调函数
     * @returns {function} 取消订阅函数
     */
    subscribe(key, callback) {
        if (!this.subscribers.has(key)) {
            this.subscribers.set(key, []);
        }
        this.subscribers.get(key).push(callback);
        
        // 如果已有数据，立即调用回调
        if (this.data.has(key)) {
            try {
                callback(this.data.get(key));
            } catch (error) {
                console.error('Initial callback error:', error);
            }
        }
        
        // 返回取消订阅函数
        return () => {
            const subscribers = this.subscribers.get(key);
            if (subscribers) {
                const index = subscribers.indexOf(callback);
                if (index > -1) {
                    subscribers.splice(index, 1);
                }
            }
        };
    }
    
    /**
     * 获取数据
     * @param {string} key - 数据键
     * @returns {any} 数据值
     */
    getData(key) {
        return this.data.get(key);
    }
    
    /**
     * 设置缓存
     * @param {string} key - 缓存键
     * @param {any} value - 缓存值
     * @param {number} ttl - 生存时间(毫秒)
     */
    setCache(key, value, ttl = 60000) {
        this.cache.set(key, value);
        
        // 清除旧的超时
        if (this.cacheTimeout.has(key)) {
            clearTimeout(this.cacheTimeout.get(key));
        }
        
        // 设置新的超时
        const timeoutId = setTimeout(() => {
            this.cache.delete(key);
            this.cacheTimeout.delete(key);
        }, ttl);
        
        this.cacheTimeout.set(key, timeoutId);
    }
    
    /**
     * 获取缓存TTL
     * @param {string} dataType - 数据类型
     * @returns {number} TTL毫秒数
     */
    getCacheTTL(dataType) {
        const ttlMap = {
            'progress': 30000,        // 30秒
            'risk_assessment': 60000, // 1分钟
            'constraints': 120000,    // 2分钟
            'knowledge_graph': 120000,// 2分钟
            'manager_status': 10000,  // 10秒
            'algorithm_logs': 5000,   // 5秒
            'deliverables': 60000     // 1分钟
        };
        return ttlMap[dataType] || 60000;
    }
    
    /**
     * 启动HTTP状态轮询（替代WebSocket）
     */
    startPolling() {
        if (!this.taskId || this.taskId === 'no-task') {
            console.warn('Cannot start polling without valid taskId');
            return;
        }

        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
        }

        this.pollingActive = true;
        console.log(`Starting HTTP polling for task: ${this.taskId}`);

        this.pollingInterval = setInterval(() => {
            this.pollStatus();
        }, 2000); // 每2秒轮询

        // 立即执行一次
        this.pollStatus();
    }

    /**
     * 停止HTTP轮询
     */
    stopPolling() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
        this.pollingActive = false;
        console.log('HTTP polling stopped');
    }

    /**
     * 轮询状态数据
     */
    async pollStatus() {
        if (!this.pollingActive || !this.taskId) {
            return;
        }

        try {
            const response = await this.httpClient.get(`http://localhost:25526/api/pm_v2/status/${this.taskId}`);
            if (response.success) {
                this.handleStatusUpdate(response.data);
            } else {
                console.warn('Status polling failed:', response.error);
            }
        } catch (error) {
            console.error('Status polling error:', error);
        }
    }

    /**
     * 处理状态更新（替代WebSocket消息处理）
     */
    handleStatusUpdate(statusData) {
        console.log('Status update received:', statusData);

        // 将状态数据转换为组件需要的格式
        const mappedData = this.mapStatusToComponents(statusData);

        // 通知所有订阅者
        for (const [dataType, data] of Object.entries(mappedData)) {
            this.setData(dataType, data);
        }
    }

    /**
     * 将状态数据映射到组件数据类型
     */
    mapStatusToComponents(statusData) {
        return {
            'progress': {
                stage: statusData.stage || 0,
                status: statusData.status || 'idle',
                message: statusData.message || '',
                progress_percentage: statusData.progress_percentage || 0
            },
            'manager_status': {
                task_id: this.taskId,
                status: statusData.status || 'idle',
                last_updated: statusData.last_updated,
                project_path: statusData.project_path
            },
            'risk_assessment': {
                status: statusData.status,
                stage: statusData.stage,
                message: statusData.message
            }
            // 可以根据需要添加更多数据类型映射
        };
    }
    
    /**
     * 处理WebSocket消息（已弃用，保留兼容性）
     * @param {object} message - WebSocket消息
     * @deprecated 使用HTTP轮询架构，此方法已弃用
     */
    handleWebSocketMessage(message) {
        const { type, data } = message;
        console.warn('WebSocket message handling is deprecated, using HTTP polling instead');
        console.log(`WebSocket message received: ${type}`, data);
        
        // 根据事件类型更新对应的数据
        switch (type) {
            case 'stage_progress_update':
                this.updateProgressData(data);
                break;
            case 'stage_zero_metrics_update':
                this.updateStageZeroMetrics(data);
                break;
            case 'key_metrics_update':
                this.updateKeyMetrics(data);
                break;
            case 'reliability_score_update':
                this.updateRiskAssessment(data);
                break;
            case 'constraint_created':
            case 'constraint_updated':
                this.updateConstraintData(data);
                break;
            case 'knowledge_graph_update':
                this.updateKnowledgeGraph(data);
                break;
            case 'manager_status_update':
                this.updateManagerStatus(data);
                break;
            case 'algorithm_log_entry':
                this.addAlgorithmLogEntry(data);
                break;
            case 'deliverable_ready':
                this.updateDeliverables(data);
                break;
            default:
                console.warn(`Unknown WebSocket message type: ${type}`);
        }
    }
    
    /**
     * 更新进度数据
     */
    updateProgressData(data) {
        const currentData = this.getData('progress') || {};
        const updatedData = { ...currentData, ...data };
        this.setData('progress', updatedData);
    }
    
    /**
     * 更新阶段零指标
     */
    updateStageZeroMetrics(data) {
        const progressData = this.getData('progress') || {};
        progressData.stage_zero_metrics = { ...progressData.stage_zero_metrics, ...data };
        this.setData('progress', progressData);
    }
    
    /**
     * 更新关键指标
     */
    updateKeyMetrics(data) {
        const progressData = this.getData('progress') || {};
        progressData.key_metrics = { ...progressData.key_metrics, ...data };
        this.setData('progress', progressData);
    }
    
    /**
     * 更新风险评估数据
     */
    updateRiskAssessment(data) {
        const currentData = this.getData('risk_assessment') || {};
        const updatedData = { ...currentData, ...data };
        this.setData('risk_assessment', updatedData);
    }
    
    /**
     * 更新约束数据
     */
    updateConstraintData(data) {
        // 更新约束列表
        const constraintsData = this.getData('constraints') || { constraints: [] };
        
        if (data.constraint) {
            const existingIndex = constraintsData.constraints.findIndex(
                c => c.id === data.constraint.id
            );
            
            if (existingIndex >= 0) {
                constraintsData.constraints[existingIndex] = data.constraint;
            } else {
                constraintsData.constraints.push(data.constraint);
            }
            
            this.setData('constraints', constraintsData);
            
            // 如果是当前选中的约束，也更新详情
            this.setData(`constraint_detail_${data.constraint.id}`, data.constraint);
        }
    }
    
    /**
     * 更新知识图谱数据
     */
    updateKnowledgeGraph(data) {
        const currentData = this.getData('knowledge_graph') || { nodes: [], connections: [] };
        
        if (data.operation === 'add_node' && data.node) {
            currentData.nodes.push(data.node);
            if (data.new_connections) {
                currentData.connections.push(...data.new_connections);
            }
        }
        
        this.setData('knowledge_graph', currentData);
    }
    
    /**
     * 更新管理器状态
     */
    updateManagerStatus(data) {
        const currentData = this.getData('manager_status') || {};
        const updatedData = { ...currentData, ...data };
        this.setData('manager_status', updatedData);
    }
    
    /**
     * 添加算法日志条目
     */
    addAlgorithmLogEntry(data) {
        const logsData = this.getData('algorithm_logs') || { process_logs: [] };
        logsData.process_logs.push(data);
        
        // 保持最近100条日志
        if (logsData.process_logs.length > 100) {
            logsData.process_logs.shift();
        }
        
        this.setData('algorithm_logs', logsData);
    }
    
    /**
     * 更新交付结果
     */
    updateDeliverables(data) {
        const currentData = this.getData('deliverables') || { output_files: [] };
        
        if (data.file) {
            const existingIndex = currentData.output_files.findIndex(
                f => f.id === data.file.id
            );
            
            if (existingIndex >= 0) {
                currentData.output_files[existingIndex] = data.file;
            } else {
                currentData.output_files.push(data.file);
            }
        }
        
        this.setData('deliverables', currentData);
    }
    
    /**
     * 获取API端点
     * @param {string} dataType - 数据类型
     * @returns {string} API端点
     */
    getEndpoint(dataType) {
        // HTTP轮询架构：所有数据都通过状态轮询获取，不再使用单独的端点
        console.warn(`getEndpoint called for ${dataType} - HTTP polling architecture uses unified status endpoint`);

        // 优先使用PM V2数据配置（如果有的话）
        if (window.PM_V2_DATA_TYPE_MAPPING && window.PM_V2_DATA_TYPE_MAPPING[dataType]) {
            return window.PM_V2_DATA_TYPE_MAPPING[dataType].endpoint;
        }

        // 对于HTTP轮询架构，返回null表示不使用单独端点
        return null;
    }

    /**
     * 获取数据类型的配置信息
     * @param {string} dataType - 数据类型
     * @returns {object} 配置信息
     */
    getDataTypeConfig(dataType) {
        if (window.PM_V2_DATA_TYPE_MAPPING && window.PM_V2_DATA_TYPE_MAPPING[dataType]) {
            return window.PM_V2_DATA_TYPE_MAPPING[dataType];
        }

        // 默认配置
        return {
            endpoint: this.getEndpoint(dataType),
            method: 'GET',
            cache: true,
            cacheTTL: 60000,
            mockData: null
        };
    }
    
    /**
     * 清理资源
     */
    destroy() {
        // 清理WebSocket连接
        if (this.wsClient) {
            this.wsClient.close();
            this.wsClient = null;
        }
        
        // 清理缓存超时
        this.cacheTimeout.forEach(timeoutId => clearTimeout(timeoutId));
        this.cacheTimeout.clear();
        
        // 清理数据
        this.data.clear();
        this.subscribers.clear();
        this.cache.clear();
        
        console.log('DataManager destroyed');
    }
}
