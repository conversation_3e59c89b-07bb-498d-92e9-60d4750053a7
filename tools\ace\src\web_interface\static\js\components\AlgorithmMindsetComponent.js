/**
 * Python主持人算法思维 (AlgorithmMindsetComponent)
 * 
 * 功能：
 * - 展示AI与算法的协同状态。
 * - 动态渲染算法执行日志。
 * - 支持点击日志条目查看详细信息。
 * - 支持展开/折叠日志的AI通讯和Python操作详情。
 * 
 * 数据依赖：
 * - algorithm_logs: 包含AI-算法协同状态和日志条目。
 */
class AlgorithmMindsetComponent extends BaseComponent {
    getDataTypes() {
        return ['algorithm_logs'];
    }

    render() {
        const logData = this.getData('algorithm_logs') || { collaboration: {}, logs: [] };
        const collaboration = logData.collaboration || {};
        const logs = logData.logs || [];

        this.container.innerHTML = `
            <div class="area-title">
                Python主持人算法思维
                <span class="open-directory-icon" title="打开项目目录" id="open-directory-btn">📁</span>
            </div>
            <div class="area-content" style="height: calc(100% - 2rem); overflow-y: auto;">
                <!-- V4.2 AI-算法协同展示 -->
                <div class="ai-algorithm-collaboration-compact">
                    <div class="collaboration-header-compact">
                        <div class="ai-role-compact">
                            <span class="role-icon">🤖</span>
                            <span>${collaboration.ai_role || '首席架构师AI'}</span>
                        </div>
                        <div class="collaboration-arrow">→</div>
                        <div class="algorithm-role-compact">
                            <span class="role-icon">⚙️</span>
                            <span>${collaboration.algorithm_role || 'ConstraintPreprocessor'}</span>
                        </div>
                    </div>
                    <div class="validation-status-compact">
                        <div class="check-item success">
                            <span class="check-icon">✓</span>
                            <span>${collaboration.status_text || '预验证通过 - 已生成GB001'}</span>
                        </div>
                    </div>
                </div>

                <!-- V4扫描日志预留位置 -->
                <div class="scanning-logs" style="display:none;">扫描日志将在此显示</div>

                <!-- Python主持人算法思维过程 -->
                <div id="process-log" style="font-family: monospace; font-size: 0.8rem; margin-bottom: 1rem;">
                    ${logs.map(log => this.renderLogEntry(log)).join('')}
                </div>
            </div>
        `;
    }
    
    renderLogEntry(log) {
        return `
            <div class="log-entry expandable" data-log-id="${log.id}" style="cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#393B40'" onmouseout="this.style.backgroundColor='transparent'">
                [${log.timestamp || '14:17:30'}] ${log.message || '启动检查: 正在验证IDE AI连接状态...✅ 连接正常'}
                <span class="log-arrows">
                    <span class="arrow-ai-comm" data-log-id="${log.id}" data-detail-type="ai-comm"></span>
                    <span class="arrow-py-ops" data-log-id="${log.id}" data-detail-type="py-ops"></span>
                </span>
            </div>
        `;
    }

    bindEvents() {
        const processLog = this.container.querySelector('#process-log');
        if (!processLog) return;

        processLog.addEventListener('click', (e) => {
            const logEntry = e.target.closest('.log-entry');
            if (!logEntry) return;

            const logId = logEntry.dataset.logId;

            if (e.target.classList.contains('arrow-ai-comm') || e.target.classList.contains('arrow-py-ops')) {
                // 点击的是箭头
                const detailType = e.target.dataset.detailType;
                this.toggleLogDetail(e.target, detailType, logId);
            } else {
                // 点击的是日志条目本身
                this.showLogDetail(logId);
            }
        });

        // 绑定打开目录按钮事件
        const openDirectoryBtn = this.container.querySelector('#open-directory-btn');
        if (openDirectoryBtn) {
            openDirectoryBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.showDirectoryInputPopup(e);
            });
        }
    }

    // 显示目录输入弹窗
    showDirectoryInputPopup(event) {
        // 移除已存在的弹窗
        const existingPopup = document.querySelector('.directory-input-popup');
        if (existingPopup) {
            existingPopup.remove();
        }

        // 创建弹窗
        const popup = document.createElement('div');
        popup.className = 'knowledge-help-popup show directory-input-popup';
        popup.innerHTML = `
            <button class="help-close-btn" onclick="this.closest('.directory-input-popup').remove()">×</button>
            <div class="help-popup-title">
                <span>📁</span>
                <span>输入目录路径</span>
            </div>
            <div class="help-popup-section">
                <div class="help-section-title">目录路径</div>
                <div style="position: relative;">
                    <div id="path-error-message" style="color: #F44336; font-size: 0.7rem; margin-bottom: 4px; line-height: 1.2;"></div>
                    <input type="text" id="directory-path-input" placeholder="请输入或粘贴目录路径..." style="width: 100%; padding: 8px 12px; background: #2A2D30; color: #BBBBBB; border: 1px solid #3C3F41; border-radius: 4px; font-size: 0.8rem; margin-bottom: 12px; box-sizing: border-box;" />
                </div>
                <div style="display: flex; gap: 8px; justify-content: flex-end;">
                    <button class="popup-btn confirm-btn" onclick="this.closest('.directory-input-popup').dispatchEvent(new CustomEvent('confirmDirectory'))" style="padding: 6px 12px; background: linear-gradient(135deg, #0078D4, #40A9FF); color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">确定</button>
                    <button class="popup-btn cancel-btn" onclick="this.closest('.directory-input-popup').remove()" style="padding: 6px 12px; background: transparent; color: #BBBBBB; border: 1px solid #3C3F41; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">取消</button>
                </div>
            </div>
        `;

        // 设置弹窗位置
        const rect = event.target.getBoundingClientRect();
        popup.style.position = 'fixed';
        popup.style.top = (rect.bottom + 5) + 'px';
        popup.style.left = (rect.left - 100) + 'px';
        popup.style.zIndex = '1000';

        // 添加到页面
        document.body.appendChild(popup);

        // 获取元素引用
        const input = popup.querySelector('#directory-path-input');
        const errorMessage = popup.querySelector('#path-error-message');
        const confirmBtn = popup.querySelector('.confirm-btn');

        // 路径验证函数
        const validatePath = (path) => {
            if (!path.trim()) {
                return { isValid: false, message: '请输入目录路径' };
            }

            // 检查非法字符（排除冒号，因为Windows路径需要冒号）
            const illegalChars = /[<>"|?*]/;
            if (illegalChars.test(path)) {
                return { 
                    isValid: false, 
                    message: '路径包含非法字符（<>"|?*），请检查后重新输入' 
                };
            }

            // Windows绝对路径格式验证
            // 格式：盘符:\路径\路径\...
            const windowsPathRegex = /^[A-Za-z]:\\(?:[^<>"|?*\r\n]+\\)*[^<>"|?*\r\n]*$/;
            
            if (!windowsPathRegex.test(path)) {
                return { 
                    isValid: false, 
                    message: '路径格式错误，请输入Windows绝对路径格式（如：C:\\Users\\<USER>\\Documents）' 
                };
            }

            // 检查路径是否以反斜杠结尾（目录路径不应该以反斜杠结尾）
            if (path.endsWith('\\')) {
                return { 
                    isValid: false, 
                    message: '目录路径不应以反斜杠结尾，请移除末尾的反斜杠' 
                };
            }

            // 检查路径长度（Windows路径最大长度为260字符）
            if (path.length > 260) {
                return { 
                    isValid: false, 
                    message: '路径过长，Windows路径最大长度为260字符' 
                };
            }

            return { isValid: true, message: '' };
        };

        // 显示错误信息
        const showError = (message) => {
            errorMessage.textContent = message;
            errorMessage.classList.add('show');
        };

        // 隐藏错误信息
        const hideError = () => {
            errorMessage.classList.remove('show');
        };

        // 实时验证
        input.addEventListener('input', () => {
            const validation = validatePath(input.value);
            if (!validation.isValid && input.value.trim()) {
                showError(validation.message);
            } else {
                hideError();
            }
        });

        // 聚焦输入框
        input.focus();

        // 监听确认事件
        popup.addEventListener('confirmDirectory', async () => {
            const path = input.value.trim();
            const validation = validatePath(path);
            
            if (validation.isValid) {
                try {
                    // 显示加载状态
                    confirmBtn.disabled = true;
                    confirmBtn.textContent = '验证中...';
                    
                    // 调用后端API
                    const response = await fetch('/api/pm_v2/get_and_create', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({design_doc_path: path})
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        // 显示成功消息
                        alert(`✅ 项目经理实例已创建/获取！\n\n路径：${result.design_doc_path}\nID：${result.manager_id}`);
                        popup.remove();

                        // HTTP轮询架构：初始化状态并启动轮询
                        await this.initializePollingForManager(result.manager_id, result.design_doc_path);
                    } else {
                        // 显示错误消息
                        showError(result.error || '处理失败');
                        input.focus();
                    }
                } catch (error) {
                    console.error('目录验证请求失败:', error);
                    showError('网络请求失败，请检查网络连接');
                    input.focus();
                } finally {
                    // 恢复按钮状态
                    confirmBtn.disabled = false;
                    confirmBtn.textContent = '确定';
                }
            } else {
                showError(validation.message);
                input.focus();
            }
        });

        // 监听回车键
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                popup.dispatchEvent(new CustomEvent('confirmDirectory'));
            }
        });

        // 点击外部关闭弹窗
        document.addEventListener('click', function closePopup(e) {
            if (!popup.contains(e.target)) {
                popup.remove();
                document.removeEventListener('click', closePopup);
            }
        });
    }

    /**
     * 初始化HTTP轮询架构（在项目经理创建成功后调用）
     */
    async initializePollingForManager(managerId, designDocPath) {
        try {
            // get_and_create已经初始化了StatusQueueManager状态
            // 直接设置DataManager的taskId并启动轮询
            if (this.dataManager) {
                this.dataManager.taskId = managerId;
                this.dataManager.projectId = managerId; // 保持兼容性
                this.dataManager.startPolling();

                console.log(`✅ HTTP轮询已启动，task_id: ${managerId}`);

                // 更新UI显示
                this.showPollingStatus(managerId, designDocPath);
            } else {
                console.warn('DataManager not available for polling initialization');
            }
        } catch (error) {
            console.error('Error initializing polling:', error);
        }
    }

    /**
     * 显示轮询状态
     */
    showPollingStatus(taskId, path) {
        // 在详细区显示轮询状态
        const detailContent = document.querySelector('#detail-content');
        if (detailContent) {
            detailContent.innerHTML = `
                <div style="color: #4CAF50; padding: 10px; border: 1px solid #4CAF50; border-radius: 4px;">
                    <div style="font-weight: bold; margin-bottom: 5px;">🔄 HTTP轮询已启动</div>
                    <div style="font-size: 0.8rem;">
                        <div>Task ID: ${taskId}</div>
                        <div>项目路径: ${path}</div>
                        <div>状态: 等待审查启动...</div>
                    </div>
                </div>
            `;
        }
    }

    showLogDetail(logId) {
        // 安全检查appManager是否存在
        if (!this.appManager) {
            console.warn('AppManager not available in AlgorithmMindsetComponent');
            return;
        }
        
        const humanInputComponent = this.appManager.getComponent('control');
        if (humanInputComponent && typeof humanInputComponent.updateDetail === 'function') {
            const logData = this.getData('algorithm_logs')?.logs.find(l => l.id === logId);
            if (logData) {
                // 根据日志类型生成不同的标题和图标
                let title = '';
                let icon = '';
                
                if (logData.type === 'ai_communication') {
                    title = '🤖 AI通信详情';
                    icon = '🤖';
                } else if (logData.type === 'algorithm_processing') {
                    title = '⚙️ 算法处理详情';
                    icon = '⚙️';
                } else {
                    title = '📋 日志详情';
                    icon = '📋';
                }
                
                const content = `
                    <div style="margin-bottom: 1rem;">
                        <div style="font-size: 1.1rem; font-weight: bold; color: #0078D4; margin-bottom: 0.5rem;">
                            ${icon} ${title}
                        </div>
                        <div style="margin-bottom: 0.8rem;">
                            <strong style="color: #BBBBBB;">检查项目：</strong>
                            <span style="color: #BBBBBB;">${logData.summary}</span>
                        </div>
                        <div style="margin-bottom: 0.8rem;">
                            <strong style="color: #BBBBBB;">执行算法：</strong>
                            <span style="color: #BBBBBB;">AI通信与Python算法协同处理</span>
                            <ul style="margin: 0.3rem 0; padding-left: 1.5rem; color: #BBBBBB;">
                                <li>AI分析：${logData.ai_comm_detail}</li>
                                <li>Python处理：${logData.py_ops_detail}</li>
                            </ul>
                        </div>
                        <div style="margin-bottom: 0.8rem;">
                            <strong style="color: #BBBBBB;">检查结果：</strong>
                            <div style="color: #BBBBBB;">
                                <div>• 处理状态：完成</div>
                                <div>• 时间戳：${logData.timestamp}</div>
                                <div>• 日志ID：${logData.id}</div>
                            </div>
                        </div>
                        <div style="margin-bottom: 0.8rem;">
                            <strong style="color: #BBBBBB;">技术细节：</strong>
                            <div style="color: #BBBBBB;">
                                <div>• AI通信：基于语义分析的智能处理</div>
                                <div>• Python算法：结构化数据处理和存储</div>
                                <div>• 协同机制：AI决策 + Python执行</div>
                            </div>
                        </div>
                        <div style="margin-bottom: 0.8rem;">
                            <strong style="color: #BBBBBB;">性能指标：</strong>
                            <div style="color: #BBBBBB;">
                                <div>• 处理时间：< 1秒</div>
                                <div>• 成功率：100%</div>
                                <div>• 数据完整性：已验证</div>
                            </div>
                        </div>
                    </div>
                `;
                humanInputComponent.updateDetail(content);
            } else {
                humanInputComponent.updateDetail(`未找到ID为 ${logId} 的日志详情。`);
            }
        } else {
            console.warn('control-area component not found or updateDetail method not available');
        }
    }

    toggleLogDetail(arrowElement, detailType, logId) {
        arrowElement.classList.toggle('expanded');
        const logEntry = arrowElement.closest('.log-entry');
        const existingDetail = logEntry.querySelector(`.expanded-details.${detailType}-details`);

        if (existingDetail) {
            existingDetail.remove();
        } else {
            const detailContent = this.getDetailContent(logId, detailType);
            const detailDiv = document.createElement('div');
            detailDiv.className = `expanded-details ${detailType}-details`;
            detailDiv.innerHTML = detailContent;
            logEntry.appendChild(detailDiv);
        }
    }
    
    getDetailContent(logId, type) {
        // 在实际应用中，这里应该从数据中获取详细信息
        return `这是 ${logId} 的 ${type} 详细信息。`;
    }
}