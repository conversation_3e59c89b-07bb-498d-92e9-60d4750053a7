// HTTP轮询架构初始化函数
function getTaskIdFromUrl() {
    const pathParts = window.location.pathname.split('/');
    return pathParts[pathParts.length - 1];
}

function getTaskIdFromTemplate() {
    // 从模板变量获取task_id（如果有的话）
    return window.taskId || null;
}

document.addEventListener('DOMContentLoaded', async () => {
    // 从URL或模板获取task_id
    let taskId = getTaskIdFromUrl();

    // 如果URL中没有task_id，尝试从模板变量获取
    if (!taskId || taskId === 'pm_v2') {
        taskId = getTaskIdFromTemplate();
    }

    console.log(`Initializing PM_V2, task ID: ${taskId || 'none'}`);

    // 创建DataManager实例
    // 如果没有task_id，不启动轮询，只显示界面让用户输入
    const dataManager = new DataManager(taskId);

    // 创建AppManager实例
    const appManager = new AppManager(taskId || 'no-task', PM_V2_DATA_TYPE_MAPPING);
    appManager.setDataManager(dataManager);

    // 注册所有PM_V2组件 (type, containerId, ComponentClass, config)
    appManager.registerComponent('progress', 'progress-area', ProjectProgressComponent);
    appManager.registerComponent('risk', 'risk-area', RiskAssessmentComponent);
    appManager.registerComponent('manager', 'manager-area', ManagerStatusComponent);
    appManager.registerComponent('algorithm', 'algorithm-area', AlgorithmMindsetComponent);
    appManager.registerComponent('constraint', 'constraint-area', ConstraintReviewComponent);
    appManager.registerComponent('knowledge', 'knowledge-area', KnowledgeBaseComponent);
    appManager.registerComponent('control', 'control-area', HumanInputComponent);
    appManager.registerComponent('deliverables', 'deliverables-area', ProjectOutputComponent);

    try {
        // 初始化所有组件
        await appManager.init();
        console.log('PM_V2 Unified Architecture Initialized Successfully.');

        // 将 appManager 和 dataManager 暴露到全局，方便调试
        window.pmV2App = appManager;
        window.pmV2DataManager = dataManager;

        console.log('PM_V2 HTTP Polling Architecture Initialized Successfully.');

        // 如果URL包含test=true，运行测试
        if (window.location.search.includes('test=true')) {
            console.log('Running PM_V2 tests...');
            if (window.PM_V2_Tests) {
                const tests = new PM_V2_Tests();
                await tests.runAllTests();
            } else {
                console.warn('PM_V2_Tests not found. Make sure pm_v2_test.js is loaded.');
            }
        }

    } catch (error) {
        console.error('Failed to initialize PM_V2 Unified Architecture:', error);
    }
});