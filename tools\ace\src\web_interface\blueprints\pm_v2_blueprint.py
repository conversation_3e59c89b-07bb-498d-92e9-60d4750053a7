# -*- coding: utf-8 -*-
"""
项目经理V2蓝图 - 处理项目经理相关的Web接口
"""

import os
import json
import asyncio
import threading
from datetime import datetime
from flask import Blueprint, request, jsonify
from flask_socketio import emit, join_room, leave_room

# 创建蓝图
pm_v2_bp = Blueprint('pm_v2', __name__, url_prefix='/api/pm_v2')

@pm_v2_bp.route('/get_and_create', methods=['POST'])
def get_and_create_manager():
    """
    获取或创建项目经理实例
    """
    from ..app import web_app
    try:
        data = request.get_json()
        if not data or 'design_doc_path' not in data:
            return jsonify({'success': False, 'error': '请求数据为空或缺少 design_doc_path 参数'}), 400

        design_doc_path = data.get('design_doc_path')

        # 调用服务层处理业务逻辑
        if web_app.project_manager_service:
            manager = web_app.project_manager_service.get_or_create_manager(design_doc_path)

            # HTTP轮询架构：同时初始化StatusQueueManager状态
            web_app.status_queue_manager.initialize_task(manager.id, {
                'status': 'initialized',
                'project_path': design_doc_path,
                'stage': 0,
                'message': '项目经理实例已创建，等待启动审查...'
            })

            print(f"🚀 [Blueprint] 已成功调用服务层为路径创建项目经理: {design_doc_path}")
            print(f"📋 [Blueprint] StatusQueueManager状态已初始化，task_id: {manager.id}")

            return jsonify({
                'success': True,
                'message': '项目经理实例已创建/获取',
                'manager_id': manager.id,
                'design_doc_path': design_doc_path
            })
        else:
            return jsonify({'success': False, 'error': '项目经理服务未初始化'}), 500

    except ValueError as e:
        print(f"❌ [Blueprint] 目录验证失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 400
    except Exception as e:
        print(f"❌ [Blueprint] 获取或创建项目经理时发生错误: {e}")
        return jsonify({'success': False, 'error': f'内部错误: {str(e)}'}), 500

# 已移除：旧的manager_status接口，统一使用 /status/{task_id} 接口


@pm_v2_bp.route('/log_directory_action', methods=['POST'])
def log_directory_action():
    """
    记录目录操作日志
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            }), 400

        action_type = data.get('action_type', 'unknown')
        directory_path = data.get('directory_path', '')
        user_input = data.get('user_input', '')
        result = data.get('result', {})

        # 记录日志
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'action_type': action_type,
            'directory_path': directory_path,
            'user_input': user_input,
            'result': result
        }

        print(f"📝 目录操作日志: {log_entry}")

        return jsonify({
            'success': True,
            'message': '日志记录成功',
            'log_entry': log_entry
        })

    except Exception as e:
        print(f"❌ 记录日志时发生错误: {e}")
        return jsonify({
            'success': False,
            'error': f'记录日志时发生内部错误: {str(e)}'
        }), 500

# 已移除：旧的list_managers接口，统一使用StatusQueueManager管理状态


# ==================== HTTP轮询架构新增API端点 ====================

@pm_v2_bp.route('/create-workspace', methods=['POST'])
def create_workspace():
    """
    创建工作区并返回task_id（HTTP轮询架构）
    """
    from ..app import web_app
    try:
        data = request.get_json()
        if not data or 'project_path' not in data:
            return jsonify({'success': False, 'error': '请求数据为空或缺少 project_path 参数'}), 400

        project_path = data.get('project_path')

        # 调用服务层创建项目经理实例
        if web_app.project_manager_service:
            manager = web_app.project_manager_service.get_or_create_manager(project_path)
            task_id = manager.id

            # 初始化状态队列
            web_app.status_queue_manager.initialize_task(task_id, {
                'status': 'initialized',
                'project_path': project_path,
                'stage': 0,
                'message': '工作区已创建，等待启动审查...'
            })

            print(f"🚀 [Blueprint] 工作区创建成功，task_id: {task_id}")

            return jsonify({
                'success': True,
                'task_id': task_id,
                'redirect_url': f'/pm_v2/{task_id}',
                'message': '工作区创建成功'
            })
        else:
            return jsonify({'success': False, 'error': '项目经理服务未初始化'}), 500

    except ValueError as e:
        print(f"❌ [Blueprint] 目录验证失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 400
    except Exception as e:
        print(f"❌ [Blueprint] 创建工作区时发生错误: {e}")
        return jsonify({'success': False, 'error': f'内部错误: {str(e)}'}), 500


@pm_v2_bp.route('/status/<task_id>', methods=['GET'])
def get_task_status(task_id):
    """
    获取任务状态（轮询接口）
    """
    from ..app import web_app
    try:
        status_data = web_app.status_queue_manager.get_status(task_id)
        if not status_data:
            return jsonify({'success': False, 'error': 'Task not found'}), 404

        return jsonify({
            'success': True,
            'data': status_data,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ [Blueprint] 获取任务状态时发生错误: {e}")
        return jsonify({'success': False, 'error': f'内部错误: {str(e)}'}), 500


@pm_v2_bp.route('/start-review/<task_id>', methods=['POST'])
def start_review(task_id):
    """
    启动审查任务
    """
    from ..app import web_app
    try:
        manager = web_app.project_manager_service.get_manager_by_id(task_id)
        if not manager:
            return jsonify({'success': False, 'error': 'Manager not found'}), 404

        # 设置状态回调
        manager.set_status_callback(web_app.status_queue_manager.update_status)

        # 启动审查（异步）
        threading.Thread(target=manager.run_admission_review, daemon=True).start()

        print(f"🚀 [Blueprint] 审查任务启动成功，task_id: {task_id}")

        return jsonify({
            'success': True,
            'message': '审查任务已启动',
            'task_id': task_id
        })

    except Exception as e:
        print(f"❌ [Blueprint] 启动审查时发生错误: {e}")
        return jsonify({'success': False, 'error': f'内部错误: {str(e)}'}), 500
