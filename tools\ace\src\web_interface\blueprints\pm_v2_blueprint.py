# -*- coding: utf-8 -*-
"""
项目经理V2蓝图 - 处理项目经理相关的Web接口
"""

import os
import json
import asyncio
from datetime import datetime
from flask import Blueprint, request, jsonify
from flask_socketio import emit, join_room, leave_room

# 创建蓝图
pm_v2_bp = Blueprint('pm_v2', __name__, url_prefix='/api/pm_v2')

@pm_v2_bp.route('/get_and_create', methods=['POST'])
def get_and_create_manager():
    """
    获取或创建项目经理实例
    """
    from ..app import web_app
    try:
        data = request.get_json()
        if not data or 'design_doc_path' not in data:
            return jsonify({'success': False, 'error': '请求数据为空或缺少 design_doc_path 参数'}), 400

        design_doc_path = data.get('design_doc_path')

        # 调用服务层处理业务逻辑
        if web_app.project_manager_service:
            manager = web_app.project_manager_service.get_or_create_manager(design_doc_path)
            
            print(f"🚀 [Blueprint] 已成功调用服务层为路径创建项目经理: {design_doc_path}")

            return jsonify({
                'success': True,
                'message': '项目经理实例已创建/获取',
                'manager_id': manager.id,
                'design_doc_path': design_doc_path
            })
        else:
            return jsonify({'success': False, 'error': '项目经理服务未初始化'}), 500

    except ValueError as e:
        print(f"❌ [Blueprint] 目录验证失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 400
    except Exception as e:
        print(f"❌ [Blueprint] 获取或创建项目经理时发生错误: {e}")
        return jsonify({'success': False, 'error': f'内部错误: {str(e)}'}), 500

@pm_v2_bp.route('/manager_status/<manager_id>', methods=['GET'])
def get_manager_status(manager_id):
    """
    获取特定项目经理的状态
    """
    try:
        # TODO: 调用服务层来获取状态
        # status = project_manager_service.get_manager_status(manager_id)
        # if status is None:
        #     return jsonify({'success': False, 'error': '项目经理实例不存在'}), 404
        
        # 模拟成功响应
        print(f"📡 [Blueprint] 接收到获取项目经理状态的请求, ID: {manager_id}")
        print(f"下一步: 将调用 ProjectManagerService 来获取状态。")
        
        status = {"state": "pending", "message": "等待服务层实现"}

        return jsonify({'success': True, 'status': status})

    except Exception as e:
        print(f"❌ [Blueprint] 获取项目经理状态时发生错误: {e}")
        return jsonify({'success': False, 'error': f'内部错误: {str(e)}'}), 500


@pm_v2_bp.route('/log_directory_action', methods=['POST'])
def log_directory_action():
    """
    记录目录操作日志
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            }), 400

        action_type = data.get('action_type', 'unknown')
        directory_path = data.get('directory_path', '')
        user_input = data.get('user_input', '')
        result = data.get('result', {})

        # 记录日志
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'action_type': action_type,
            'directory_path': directory_path,
            'user_input': user_input,
            'result': result
        }

        print(f"📝 目录操作日志: {log_entry}")

        return jsonify({
            'success': True,
            'message': '日志记录成功',
            'log_entry': log_entry
        })

    except Exception as e:
        print(f"❌ 记录日志时发生错误: {e}")
        return jsonify({
            'success': False,
            'error': f'记录日志时发生内部错误: {str(e)}'
        }), 500

@pm_v2_bp.route('/list_managers', methods=['GET'])
def list_project_managers():
    """
    列出所有活跃的项目经理实例
    """
    try:
        # TODO: 调用服务层来获取所有实例信息
        # managers_info = project_manager_service.list_all_managers()

        # 模拟成功响应
        print(f"📋 [Blueprint] 接收到列出所有项目经理的请求。")
        print(f"下一步: 将调用 ProjectManagerService 来获取列表。")
        
        managers_info = []

        return jsonify({'success': True, 'managers': managers_info})

    except Exception as e:
        print(f"❌ [Blueprint] 列出项目经理时发生错误: {e}")
        return jsonify({'success': False, 'error': f'内部错误: {str(e)}'}), 500
