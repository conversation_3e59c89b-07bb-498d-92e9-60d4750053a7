# 技术背景 (Tech Context)

*此文档记录了项目使用的技术、开发设置和技术限制。*

## 技术栈 (Technologies Used)

- **后端框架**: Flask + SocketIO
- **前端技术**: HTML5, CSS3, JavaScript (ES6+)
- **API设计**: RESTful API + WebSocket
- **蓝图架构**: Flask Blueprint模式
- **数据模型**: 统一语义模型 (AtomicConstraint)
- **开发模式**: 渐进式实现 + 模块化设计

## 开发设置 (Development Setup)

- **项目结构**: 基于Flask的Web应用，使用蓝图模式组织代码
- **API端点**: `/api/pm_v2/*` 前缀的PM_V2相关API
- **前端组件**: 基于九宫格界面的模块化组件设计
- **开发服务器**: 运行在 `http://localhost:25526`
- **PM_V2工作台**: 访问 `http://localhost:25526/pm_v2`

## 技术限制 (Technical Constraints)

- **路径格式**: 目前仅支持Windows路径格式验证
- **API调用**: 采用单一职责API模式，业务逻辑从视图层分离。
- **前端兼容性**: 需要现代浏览器支持ES6+特性
- **服务器端口**: 固定使用25526端口，避免端口冲突
- **业务流程**: 通过对`/api/pm_v2/get_and_create`的单次调用，原子化地完成目录验证和项目经理实例化。

## 依赖项 (Dependencies)

- **Flask**: Web框架核心
- **Flask-SocketIO**: WebSocket支持
- **Python标准库**: os, json, datetime等
- **前端库**: Socket.IO客户端
- **开发工具**: 统一的配置管理器

---

## 核心架构组件 (Core Architectural Components) - V4.2

- **`AtomicConstraint` 统一语义模型**: **(核心)** 系统的**唯一**核心数据模型，用于承载所有类型的“原子知识单元”。它通过可扩展的`category`字段进行语义分类，并通过`params`字段容纳结构化数据，是整个系统可扩展性的基石。

- **两阶段知识提炼管道 (Two-Stage Knowledge Pipeline)**:
    - **`LayerAndIntentClassifier` (AI)**: 管道的第一站，负责对原始文本进行“意图分类”，产出`guideline_mapping`。
    - **`ConstraintPreprocessor` (算法 + AI)**: 管道的第二站，负责对带有“意图”的文本进行“实体分类”，并将其**转化**为结构化的`AtomicConstraint`对象，存入“全局知识库”。

- **引用式分叉机制 (Referential Branching Engine)**: *(与V4.1保持一致)* 这是`AtomicConstraint`在不同层级间传递的核心机制，通过`id`和`parent_id`实现知识的继承、细化与追溯。

- **插件式验证器架构 (Pluggable Validator Architecture)**:
    - **`ValidationLoop` (微核)**: 极其简洁、稳定的核心验证引擎，负责根据`validation_type`调度插件。
    - **Validator Plugins (插件)**: 一系列独立的、可扩展的验证器，每个都负责处理一种特定的`category`（如`StateMachineValidator`），是系统专业能力的体现。

- **分层AI角色与统一模型交互提示词 (Layered AI Personas & Unified Model Prompts)**: 系统的AI交互模型。AI的核心职责是**理解`category`**并消费和生产统一的`AtomicConstraint`模型。

- **“服务-经理”多实例管理架构 (Service-Manager Multi-Instance Architecture)**:
    - **`ProjectManagerService` (单例服务)**: 作为应用级的唯一入口，负责根据项目路径创建、管理和分发`ProjectManager`实例。
    - **`ProjectManager` (实例类)**: 每个实例与一个项目绑定，独立负责该项目的完整治理生命周期，确保了多项目并行处理时的状态隔离。
