/**
 * 统一组件基类 - 所有组件的基础类
 */
class BaseComponent {
    constructor(containerId, dataManager, config = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.dataManager = dataManager;
        this.config = config;
        this.appManager = config.appManager || null; // 添加appManager引用
        this.httpClient = dataManager.httpClient; // 添加httpClient访问
        this.subscriptions = [];
        this.isDestroyed = false;
        this.isInitialized = false;
        this.loadingState = false;
        this.errorState = null;
        
        if (!this.container) {
            throw new Error(`Container element with ID '${containerId}' not found`);
        }
        
        // 为容器添加组件标识
        this.container.setAttribute('data-component', this.constructor.name);
        this.container.setAttribute('data-component-id', containerId);
    }
    
    /**
     * 初始化组件
     * @returns {Promise} 初始化Promise
     */
    async init() {
        if (this.isInitialized) {
            console.warn(`Component ${this.containerId} is already initialized`);
            return;
        }
        
        try {
            console.log(`Initializing component: ${this.containerId}`);
            
            // 显示加载状态
            this.setLoadingState(true);
            
            // 加载初始数据
            await this.loadData();
            
            // 设置数据订阅
            this.setupSubscriptions();
            
            // 首次渲染
            this.render();
            
            // 绑定事件
            this.bindEvents();
            
            // 标记为已初始化
            this.isInitialized = true;
            this.setLoadingState(false);
            
            console.log(`Component ${this.containerId} initialized successfully`);
            
            // 触发初始化完成事件
            this.onInitialized();
            
        } catch (error) {
            this.setLoadingState(false);
            this.handleError(error);
            throw error;
        }
    }
    
    /**
     * 加载初始数据 - 子类重写
     * @returns {Promise} 数据加载Promise
     */
    async loadData() {
        const dataTypes = this.getDataTypes();
        if (!dataTypes || dataTypes.length === 0) {
            return;
        }
        
        const promises = dataTypes.map(async (type) => {
            try {
                const params = this.getDataParams(type);
                await this.dataManager.fetchData(type, params);
            } catch (error) {
                console.error(`Failed to load data type ${type}:`, error);
                // 不抛出错误，允许其他数据类型继续加载
            }
        });
        
        await Promise.all(promises);
    }
    
    /**
     * 设置数据订阅
     */
    setupSubscriptions() {
        const dataTypes = this.getDataTypes();
        if (!dataTypes || dataTypes.length === 0) {
            return;
        }
        
        dataTypes.forEach(type => {
            const unsubscribe = this.dataManager.subscribe(type, (data, oldData) => {
                if (!this.isDestroyed) {
                    this.onDataUpdate(type, data, oldData);
                }
            });
            this.subscriptions.push(unsubscribe);
        });
        
        console.log(`Component ${this.containerId} subscribed to data types:`, dataTypes);
    }
    
    /**
     * 数据更新处理 - 子类重写
     * @param {string} dataType - 数据类型
     * @param {any} data - 新数据
     * @param {any} oldData - 旧数据
     */
    onDataUpdate(dataType, data, oldData) {
        console.log(`Data updated in ${this.containerId}:`, dataType, data);
        
        // 默认行为：重新渲染
        this.render();
    }
    
    /**
     * 渲染组件 - 子类必须实现
     */
    render() {
        throw new Error(`render method must be implemented in ${this.constructor.name}`);
    }
    
    /**
     * 绑定事件 - 子类重写
     */
    bindEvents() {
        // 默认实现为空，子类可以重写
    }
    
    /**
     * 初始化完成回调 - 子类重写
     */
    onInitialized() {
        // 默认实现为空，子类可以重写
    }
    
    /**
     * 设置加载状态
     * @param {boolean} loading - 是否加载中
     */
    setLoadingState(loading) {
        this.loadingState = loading;
        
        if (loading) {
            this.renderLoading();
        }
    }
    
    /**
     * 渲染加载状态
     */
    renderLoading() {
        if (!this.container) return;
        
        this.container.innerHTML = `
            <div class="component-loading" style="
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
                color: #888;
                font-size: 0.9rem;
            ">
                <div style="text-align: center;">
                    <div style="margin-bottom: 0.5rem;">
                        <span style="
                            display: inline-block;
                            width: 20px;
                            height: 20px;
                            border: 2px solid #333;
                            border-top: 2px solid #0078D4;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                        "></span>
                    </div>
                    <div>加载中...</div>
                </div>
            </div>
            <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `;
    }
    
    /**
     * 错误处理
     * @param {Error} error - 错误对象
     */
    handleError(error) {
        console.error(`Error in component ${this.containerId}:`, error);
        
        this.errorState = error;
        this.renderError(error);
        
        // 触发错误事件
        this.onError(error);
    }
    
    /**
     * 渲染错误状态
     * @param {Error} error - 错误对象
     */
    renderError(error) {
        if (!this.container) return;
        
        const errorMessage = error.message || '未知错误';
        
        this.container.innerHTML = `
            <div class="component-error" style="
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
                color: #F44336;
                font-size: 0.9rem;
                text-align: center;
                padding: 1rem;
            ">
                <div>
                    <div style="font-size: 1.5rem; margin-bottom: 0.5rem;">⚠️</div>
                    <div style="font-weight: bold; margin-bottom: 0.5rem;">加载失败</div>
                    <div style="font-size: 0.8rem; color: #888; margin-bottom: 1rem;">${errorMessage}</div>
                    <button onclick="this.closest('[data-component-id]').component?.retry()" style="
                        background: #0078D4;
                        color: white;
                        border: none;
                        padding: 0.5rem 1rem;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 0.8rem;
                    ">重试</button>
                </div>
            </div>
        `;
        
        // 将组件实例绑定到容器，供重试按钮使用
        this.container.component = this;
    }
    
    /**
     * 错误回调 - 子类重写
     * @param {Error} error - 错误对象
     */
    onError(error) {
        // 默认实现为空，子类可以重写
    }
    
    /**
     * 重试初始化
     */
    async retry() {
        console.log(`Retrying component: ${this.containerId}`);
        
        this.errorState = null;
        this.isInitialized = false;
        
        try {
            await this.init();
        } catch (error) {
            console.error(`Retry failed for component ${this.containerId}:`, error);
        }
    }
    
    /**
     * 刷新数据
     * @param {string} dataType - 可选的特定数据类型
     */
    async refresh(dataType = null) {
        if (!this.isInitialized) {
            console.warn(`Component ${this.containerId} is not initialized`);
            return;
        }
        
        try {
            if (dataType) {
                const params = this.getDataParams(dataType);
                await this.dataManager.fetchData(dataType, params, false); // 不使用缓存
            } else {
                await this.loadData();
            }
        } catch (error) {
            this.handleError(error);
        }
    }
    
    /**
     * 获取数据
     * @param {string} dataType - 数据类型
     * @returns {any} 数据
     */
    getData(dataType) {
        return this.dataManager.getData(dataType);
    }
    
    /**
     * 显示/隐藏组件
     * @param {boolean} visible - 是否可见
     */
    setVisible(visible) {
        if (this.container) {
            this.container.style.display = visible ? '' : 'none';
        }
    }
    
    /**
     * 启用/禁用组件
     * @param {boolean} enabled - 是否启用
     */
    setEnabled(enabled) {
        if (this.container) {
            this.container.style.pointerEvents = enabled ? '' : 'none';
            this.container.style.opacity = enabled ? '' : '0.5';
        }
    }
    
    /**
     * 销毁组件
     */
    destroy() {
        if (this.isDestroyed) {
            return;
        }
        
        console.log(`Destroying component: ${this.containerId}`);
        
        // 取消所有订阅
        this.subscriptions.forEach(unsubscribe => {
            try {
                unsubscribe();
            } catch (error) {
                console.error('Error unsubscribing:', error);
            }
        });
        this.subscriptions = [];
        
        // 清理容器
        if (this.container) {
            this.container.innerHTML = '';
            this.container.removeAttribute('data-component');
            this.container.removeAttribute('data-component-id');
            delete this.container.component;
        }
        
        // 标记为已销毁
        this.isDestroyed = true;
        this.isInitialized = false;
        
        // 触发销毁事件
        this.onDestroyed();
    }
    
    /**
     * 销毁回调 - 子类重写
     */
    onDestroyed() {
        // 默认实现为空，子类可以重写
    }
    
    // 抽象方法 - 子类必须实现
    
    /**
     * 获取组件需要的数据类型 - 子类必须实现
     * @returns {Array<string>} 数据类型数组
     */
    getDataTypes() {
        throw new Error(`getDataTypes method must be implemented in ${this.constructor.name}`);
    }
    
    /**
     * 获取数据参数 - 子类重写
     * @param {string} dataType - 数据类型
     * @returns {object} 数据参数
     */
    getDataParams(dataType) {
        return {};
    }
    
    // 工具方法
    
    /**
     * 创建DOM元素
     * @param {string} tag - 标签名
     * @param {object} attributes - 属性对象
     * @param {string} content - 内容
     * @returns {HTMLElement} DOM元素
     */
    createElement(tag, attributes = {}, content = '') {
        const element = document.createElement(tag);
        
        Object.entries(attributes).forEach(([key, value]) => {
            if (key === 'style' && typeof value === 'object') {
                Object.assign(element.style, value);
            } else {
                element.setAttribute(key, value);
            }
        });
        
        if (content) {
            element.innerHTML = content;
        }
        
        return element;
    }
    
    /**
     * 查找子元素
     * @param {string} selector - CSS选择器
     * @returns {HTMLElement} 子元素
     */
    find(selector) {
        return this.container ? this.container.querySelector(selector) : null;
    }
    
    /**
     * 查找所有子元素
     * @param {string} selector - CSS选择器
     * @returns {NodeList} 子元素列表
     */
    findAll(selector) {
        return this.container ? this.container.querySelectorAll(selector) : [];
    }
    
    /**
     * 防抖函数
     * @param {function} func - 要防抖的函数
     * @param {number} delay - 延迟时间(毫秒)
     * @returns {function} 防抖后的函数
     */
    debounce(func, delay) {
        let timeoutId;
        return (...args) => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }
    
    /**
     * 节流函数
     * @param {function} func - 要节流的函数
     * @param {number} delay - 延迟时间(毫秒)
     * @returns {function} 节流后的函数
     */
    throttle(func, delay) {
        let lastCall = 0;
        return (...args) => {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                func.apply(this, args);
            }
        };
    }
}

// 导出基类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BaseComponent;
} else {
    window.BaseComponent = BaseComponent;
}
