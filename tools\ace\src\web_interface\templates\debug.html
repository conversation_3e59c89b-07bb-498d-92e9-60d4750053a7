{% extends "base.html" %}

{% block title %}调试中心 - 四重验证会议系统{% endblock %}

{% block page_title %}调试中心{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="{{ url_for('static', filename='debug.css') }}">
{% endblock %}

{% block content %}
<div class="debug-dashboard">
    <h2>调试中心</h2>
    <p class="debug-intro">基于MCP约束的统一调试界面 - 替代console输出</p>
    
    <!-- 调试信息概览 -->
    <div class="debug-overview">
        <div class="debug-card">
            <h3>调试约束</h3>
            <div class="debug-info">
                <p><strong>Console可见性:</strong> {{ debug_info.console_invisible }}</p>
                <p><strong>推荐方法:</strong> {{ debug_info.recommended_method }}</p>
                <p><strong>可用方法:</strong> {{ debug_info.available_debug_methods|length }} 种</p>
            </div>
        </div>
        
        <div class="debug-card">
            <h3>系统状态</h3>
            <div class="debug-info">
                <p><strong>总请求:</strong> <span id="debug-total-requests">{{ status.total_requests }}</span></p>
                <p><strong>活跃会话:</strong> <span id="debug-active-sessions">{{ status.active_sessions }}</span></p>
                <p><strong>系统健康:</strong> <span id="debug-system-health">{{ status.system_health }}</span></p>
            </div>
        </div>
    </div>
    
    <!-- 调试工具栏 -->
    <div class="debug-toolbar">
        <button class="debug-btn" onclick="requestDebugInfo()">获取调试信息</button>
        <button class="debug-btn" onclick="clearDebugLog()">清空日志</button>
        <button class="debug-btn" onclick="exportDebugLog()">导出日志</button>
        <button class="debug-btn" onclick="testConnection()">测试连接</button>
        <button class="debug-btn" onclick="testValidationDrivenExecutor()">🧪 测试执行器</button>
        <button class="debug-btn restart-btn" id="restart-btn" onclick="restartServer()">🔄 重启服务器</button>
    </div>
    
    <!-- 实时调试日志 -->
    <div class="debug-log-section">
        <h3>实时调试日志</h3>
        <div class="debug-log-container" id="debug-log-container">
            <!-- 删除硬编码的假调试信息 - 让服务器端真正的debug_log推送 -->
        </div>
    </div>
    
    <!-- 调试命令面板 -->
    <div class="debug-command-panel">
        <h3>调试命令</h3>
        <div class="command-input-group">
            <input type="text" id="debug-command-input" placeholder="输入调试命令..." />
            <button onclick="executeDebugCommand()">执行</button>
        </div>
        <div class="command-suggestions">
            <span class="command-suggestion" onclick="setCommand('status')">status</span>
            <span class="command-suggestion" onclick="setCommand('health')">health</span>
            <span class="command-suggestion" onclick="setCommand('clear')">clear</span>
            <span class="command-suggestion" onclick="setCommand('help')">help</span>
            <span class="command-suggestion" onclick="setCommand('mcp_test_delay')">mcp_test_delay</span>
            <span class="command-suggestion" onclick="setCommand('mcp_file_read')">mcp_file_read</span>
            <span class="command-suggestion" onclick="setCommand('mcp_file_write')">mcp_file_write</span>
            <span class="command-suggestion" onclick="setCommand('task_queue_status')">task_queue_status</span>
            <span class="command-suggestion" onclick="setCommand('batch_test')">batch_test</span>
            <span class="command-suggestion" onclick="setCommand('transaction_test')">transaction_test</span>
            <span class="command-suggestion" onclick="setCommand('client_states')">client_states</span>
            <span class="command-suggestion" onclick="setCommand('reconnection_history')">reconnection_history</span>
            <span class="command-suggestion" onclick="setCommand('release_suspend')">release_suspend</span>
            <span class="command-suggestion" onclick="setCommand('document_edit_test')">document_edit_test</span>
            <span class="command-suggestion" onclick="setCommand('read_line_test')">read_line_test</span>
            <span class="command-suggestion" onclick="setCommand('update_line_test')">update_line_test</span>
            <span class="command-suggestion" onclick="setCommand('delete_line_test')">delete_line_test</span>
            <span class="command-suggestion" onclick="setCommand('replace_all_test')">replace_all_test</span>
            <span class="command-suggestion" onclick="setCommand('directory_operation_test')">directory_operation_test</span>
            <span class="command-suggestion" onclick="setCommand('search_files_test')">search_files_test</span>
            <span class="command-suggestion" onclick="setCommand('delete_test')">delete_test</span>
            <span class="command-suggestion" onclick="setCommand('failed_api_test')">failed_api_test</span>
            <span class="command-suggestion" onclick="setCommand('copy_file_test')">copy_file_test</span>
            <span class="command-suggestion" onclick="setCommand('test_v45_abstraction')">test_v45_abstraction</span>
            <span class="command-suggestion restart-suggestion" onclick="setCommand('restart_server')">restart_server</span>
        </div>
    </div>
    
    <!-- 可用调试方法 -->
    <div class="debug-methods">
        <h3>可用调试方法</h3>
        <div class="methods-grid">
            {% for method in debug_info.available_debug_methods %}
            <div class="method-card">
                <h4>{{ method }}</h4>
                <p>{{ method }} 调试方法已启用</p>
                <button class="method-btn" onclick="testMethod('{{ method }}')">测试</button>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<style>
/* 调试中心特定样式 */
.debug-dashboard {
    max-width: 100%;
}

.debug-intro {
    color: var(--text-secondary);
    font-style: italic;
    margin-bottom: 30px;
}

.debug-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.debug-card {
    background-color: var(--bg-primary);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid var(--warning-color);
}

.debug-card h3 {
    margin: 0 0 15px 0;
    color: var(--warning-color);
}

.debug-info p {
    margin: 8px 0;
    color: var(--text-secondary);
}

.debug-toolbar {
    display: flex;
    gap: 15px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.debug-btn {
    background-color: var(--warning-color);
    color: var(--bg-primary);
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.debug-btn:hover {
    background-color: #e68900;
}

/* 重启按钮特殊样式 */
.restart-btn {
    background-color: #ff4444 !important;
    border: 2px solid #ff4444 !important;
    font-weight: bold;
    color: white !important;
}

.restart-btn:hover {
    background-color: #ff6666 !important;
    border-color: #ff6666 !important;
    box-shadow: 0 4px 8px rgba(255, 68, 68, 0.4) !important;
}

/* 重启命令建议样式 */
.restart-suggestion {
    background-color: #ff4444 !important;
    color: white !important;
    border: 1px solid #ff4444 !important;
}

.restart-suggestion:hover {
    background-color: #ff6666 !important;
    border-color: #ff6666 !important;
}

/* 重启按钮禁用状态 */
.restart-btn:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    background-color: #999 !important;
}

.restart-btn:disabled:hover {
    background-color: #999 !important;
    transform: none !important;
    box-shadow: none !important;
}

.debug-log-section {
    margin: 30px 0;
}

.debug-log-container {
    background-color: #000;
    border: 1px solid var(--warning-color);
    border-radius: 5px;
    padding: 15px;
    max-height: 400px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.85em;
}

.debug-log-entry {
    display: flex;
    gap: 12px;
    padding: 3px 0;
    border-bottom: 1px solid #333;
}

.debug-timestamp {
    color: #888;
    min-width: 140px;
    font-size: 0.8em;
}

.debug-level {
    min-width: 50px;
    font-weight: bold;
}

.debug-level.info {
    color: var(--accent-color);
}

.debug-level.warning {
    color: var(--warning-color);
}

.debug-level.error {
    color: var(--error-color);
}

.debug-level.debug {
    color: #9c27b0;
}

.debug-source {
    min-width: 80px;
    color: #2196f3;
    font-weight: bold;
}

.debug-message {
    color: var(--text-primary);
    flex: 1;
}

.debug-command-panel {
    margin: 30px 0;
}

.command-input-group {
    display: flex;
    gap: 10px;
    margin: 15px 0;
}

.command-input-group input {
    flex: 1;
    padding: 10px;
    background-color: var(--bg-primary);
    border: 1px solid var(--warning-color);
    border-radius: 5px;
    color: var(--text-primary);
}

.command-input-group button {
    background-color: var(--warning-color);
    color: var(--bg-primary);
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
}

.command-suggestions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.command-suggestion {
    background-color: var(--bg-primary);
    color: var(--warning-color);
    padding: 5px 12px;
    border-radius: 3px;
    cursor: pointer;
    border: 1px solid var(--warning-color);
    font-family: monospace;
}

.command-suggestion:hover {
    background-color: var(--warning-color);
    color: var(--bg-primary);
}

.debug-methods {
    margin: 30px 0;
}

.methods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.method-card {
    background-color: var(--bg-primary);
    padding: 15px;
    border-radius: 5px;
    border: 1px solid var(--accent-color);
}

.method-card h4 {
    margin: 0 0 10px 0;
    color: var(--accent-color);
}

.method-btn {
    background-color: var(--accent-color);
    color: var(--text-primary);
    border: none;
    padding: 8px 16px;
    border-radius: 3px;
    cursor: pointer;
    margin-top: 10px;
}

@media (max-width: 768px) {
    .debug-overview {
        grid-template-columns: 1fr;
    }
    
    .debug-toolbar {
        flex-direction: column;
    }
    
    .command-input-group {
        flex-direction: column;
    }
}
</style>
{% endblock %}

{% block extra_scripts %}
<script src="{{ url_for('static', filename='debug/validation_driven_executor_tests.js') }}"></script>
<script>
// 调试中心特定JavaScript功能
function requestDebugInfo() {
    socket.emit('debug_request', {
        action: 'get_debug_info',
        timestamp: new Date().toISOString()
    });
    addDebugLogEntry('DEBUG', 'SYSTEM', '请求调试信息');
}

function clearDebugLog() {
    document.getElementById('debug-log-container').innerHTML = '';
    addDebugLogEntry('INFO', 'SYSTEM', '调试日志已清空');
}

function exportDebugLog() {
    const logContainer = document.getElementById('debug-log-container');
    const logText = logContainer.innerText;
    const blob = new Blob([logText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `debug-log-${new Date().toISOString().slice(0, 19)}.txt`;
    a.click();
    URL.revokeObjectURL(url);
    addDebugLogEntry('INFO', 'SYSTEM', '调试日志已导出');
}

function testConnection() {
    socket.emit('request_status');
    addDebugLogEntry('DEBUG', 'NETWORK', '测试Socket.IO连接');
}

// 通用调试器测试函数 - 使用universal_debugger.js
function testValidationDrivenExecutor() {
    const testBtn = document.querySelector('button[onclick="testValidationDrivenExecutor()"]');
    if (testBtn) {
        testBtn.disabled = true;
        testBtn.textContent = '🧪 测试中...';
    }
    addDebugLogEntry('INFO', 'DEBUGGER', '🧪 启动通用调试器测试 - 串行执行模式');

    // 串行执行测试用例，避免并发问题
    executeTestSequentially().finally(() => {
        if (testBtn) {
            testBtn.disabled = false;
            testBtn.textContent = '🧪 测试执行器';
        }
    });
}

// 串行执行测试用例
async function executeTestSequentially() {
    try {
        // 测试用例1：代码生成测试
        addDebugLogEntry('INFO', 'TEST_SEQUENCE', '📝 开始测试用例1：代码生成测试');
        await executeSingleTest('testExecutorCodeGeneration');
        
        // 等待3秒后执行测试用例2
        await sleep(3000);
        
        // 测试用例2：文档编写测试
        addDebugLogEntry('INFO', 'TEST_SEQUENCE', '📄 开始测试用例2：文档编写测试');
        await executeSingleTest('testExecutorDocumentWriting');
        
        // 等待3秒后执行测试用例3
        await sleep(3000);
        
        // 测试用例3：验证失败测试
        addDebugLogEntry('INFO', 'TEST_SEQUENCE', '❌ 开始测试用例3：验证失败测试');
        await executeSingleTest('testExecutorValidationFailure');
        
        addDebugLogEntry('SUCCESS', 'TEST_SEQUENCE', '✅ 所有测试用例串行执行完成');
        
    } catch (error) {
        addDebugLogEntry('ERROR', 'TEST_SEQUENCE', `❌ 测试序列执行失败: ${error.message}`);
    }
}

// 执行单个测试用例
async function executeSingleTest(testFunctionName) {
    return new Promise((resolve, reject) => {
        try {
            // 根据测试函数名执行对应的测试
            switch (testFunctionName) {
                case 'testExecutorCodeGeneration':
                    // 直接等待测试完成
                    testExecutorCodeGeneration().then(resolve).catch(reject);
                    break;
                case 'testExecutorDocumentWriting':
                    // 直接等待测试完成
                    testExecutorDocumentWriting().then(resolve).catch(reject);
                    break;
                case 'testExecutorValidationFailure':
                    // 直接等待测试完成
                    testExecutorValidationFailure().then(resolve).catch(reject);
                    break;
                default:
                    throw new Error(`未知的测试函数: ${testFunctionName}`);
            }
            
        } catch (error) {
            reject(error);
        }
    });
}

// 延时函数
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}


function restartServer() {
    const restartBtn = document.getElementById('restart-btn');

    // 开始重启
    updateRestartButton(restartBtn, '🔄 重启中...', true);
    addDebugLogEntry('INFO', 'RESTART', '🚨 开始安全重启流程...');

    // 发送重启请求
    fetch('/api/restart_server', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ confirm: true })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            addDebugLogEntry('INFO', 'RESTART', '✅ 重启请求已发送');
            startRestartStatusCheck(restartBtn);
        } else {
            updateRestartButton(restartBtn, '❌ 失败', false);
            addDebugLogEntry('ERROR', 'RESTART', `❌ 重启失败: ${data.message}`);
            setTimeout(() => updateRestartButton(restartBtn, '🔄 重启服务器', false), 3000);
        }
    })
    .catch(error => {
        // 服务器关闭是正常的
        addDebugLogEntry('INFO', 'RESTART', '🔄 服务器正在重启...');
        startRestartStatusCheck(restartBtn);
    });
}

function updateRestartButton(button, text, disabled) {
    button.textContent = text;
    button.disabled = disabled;
    if (disabled) {
        button.style.opacity = '0.6';
        button.style.cursor = 'not-allowed';
    } else {
        button.style.opacity = '1';
        button.style.cursor = 'pointer';
    }
}

function startRestartStatusCheck(restartBtn) {
    let checkCount = 0;
    const maxChecks = 20; // 最多检查20秒

    const checkInterval = setInterval(() => {
        checkCount++;
        const dots = '.'.repeat((checkCount % 3) + 1);
        updateRestartButton(restartBtn, `🔄 重启中${dots}`, true);

        fetch('/api/system_status')
        .then(response => {
            if (response.ok) {
                clearInterval(checkInterval);
                updateRestartButton(restartBtn, '✅ 成功', false);
                addDebugLogEntry('INFO', 'RESTART', '✅ 重启成功！');
                setTimeout(() => {
                    updateRestartButton(restartBtn, '🔄 重启服务器', false);
                    window.location.reload();
                }, 1000);
            }
        })
        .catch(error => {
            if (checkCount >= maxChecks) {
                clearInterval(checkInterval);
                updateRestartButton(restartBtn, '⚠️ 超时', false);
                addDebugLogEntry('WARNING', 'RESTART', '⚠️ 重启超时');
                setTimeout(() => updateRestartButton(restartBtn, '🔄 重启服务器', false), 3000);
            }
        });
    }, 1000);
}

function executeDebugCommand() {
    const input = document.getElementById('debug-command-input');
    const command = input.value.trim();

    if (!command) return;

    // 发送命令执行到服务器端，让服务器端记录debug_log
    socket.emit('debug_command', {command: command});

    // 处理调试命令
    switch (command.toLowerCase()) {
        case 'status':
            socket.emit('request_status');
            break;
        case 'health':
            fetch('/api/health')
                .then(response => response.json())
                .then(data => {
                    addDebugLogEntry('INFO', 'API', `健康状态: ${data.status}`);
                });
            break;
        case 'clear':
            clearDebugLog();
            break;
        case 'help':
            addDebugLogEntry('INFO', 'HELP', '可用命令: status, health, clear, help, restart_server');
            addDebugLogEntry('INFO', 'HELP', 'MCP测试: mcp_test_delay, mcp_file_read, mcp_file_write');
            addDebugLogEntry('INFO', 'HELP', '事务测试: task_queue_status, batch_test, transaction_test');
            addDebugLogEntry('INFO', 'HELP', '状态管理: client_states, reconnection_history');
            addDebugLogEntry('INFO', 'HELP', 'V4.5新功能: document_edit_test, directory_operation_test, copy_file_test');
            addDebugLogEntry('INFO', 'HELP', 'V45专项测试: failed_api_test - 专门测试delete_file, delete_line, insert_line三个失败API');
            addDebugLogEntry('INFO', 'HELP', 'V45抽象层: test_v45_abstraction - 一键测试所有27个API，三重验证');
            addDebugLogEntry('INFO', 'HELP', '服务器管理: restart_server - 安全重启服务器');
            addDebugLogEntry('INFO', 'HELP', '自动化调试: 测试任务完成后自动解除挂起，无需手动操作');
            break;
        case 'mcp_test_delay':
            executeMCPTestDelay();
            break;
        case 'mcp_file_read':
            executeMCPFileRead();
            break;
        case 'mcp_file_write':
            executeMCPFileWrite();
            break;
        case 'task_queue_status':
            executeTaskQueueStatus();
            break;
        case 'batch_test':
            executeBatchTest();
            break;
        case 'transaction_test':
            executeTransactionTest();
            break;
        case 'client_states':
            executeClientStatesQuery();
            break;
        case 'reconnection_history':
            executeReconnectionHistoryQuery();
            break;
        case 'document_edit_test':
            executeDocumentEditTest();
            break;
        case 'read_line_test':
            executeReadLineTest();
            break;
        case 'update_line_test':
            executeUpdateLineTest();
            break;
        case 'delete_line_test':
            executeDeleteLineTest();
            break;
        case 'replace_all_test':
            executeReplaceAllTest();
            break;
        case 'directory_operation_test':
            executeDirectoryOperationTest();
            break;
        case 'search_files_test':
            executeSearchFilesTest();
            break;
        case 'delete_test':
            executeDeleteTest();
            break;
        case 'failed_api_test':
            executeFailedApiTest();
            break;
        case 'copy_file_test':
            executeCopyFileTest();
            break;
        case 'test_v45_abstraction':
            executeV45AbstractionTest();
            break;
        case 'restart_server':
            restartServer();
            break;
        default:
            addDebugLogEntry('WARNING', 'SYSTEM', `未知命令: ${command}`);
    }
    
    input.value = '';
}

function setCommand(command) {
    document.getElementById('debug-command-input').value = command;
}

function testMethod(method) {
    addDebugLogEntry('DEBUG', 'TEST', `测试调试方法: ${method}`);
    
    // 模拟方法测试
    setTimeout(() => {
        addDebugLogEntry('INFO', 'TEST', `${method} 方法测试完成`);
    }, 500);
}

// V4.5 MCP分离架构延时测试函数
function executeMCPTestDelay() {
    addDebugLogEntry('INFO', 'MCP_TEST', '🚀 启动延时MCP测试 - 5秒后发送任务给挂起的MCP客户端');

    // 5秒延时后发送任务
    setTimeout(() => {
        const testTask = {
            task_type: "file_operation",
            command: {
                operation: "read",
                file_path: "docs/features/F007-建立Commons库的治理机制-20250610/nexus万用插座/design/v1/fork/四重会议/todo2/promte/9-BS独立/V45-MCP分离架构设计要求.md"
            },
            metadata: {
                priority: "normal",
                timeout: 300,
                test_type: "delay_batch_test"
            }
        };

        addDebugLogEntry('INFO', 'MCP_TEST', '⏰ 延时结束，发送任务到MCP客户端...');
        sendMCPTask(testTask, 'delay_test');
    }, 5000);
}

function executeMCPFileRead() {
    addDebugLogEntry('INFO', 'MCP_TEST', '📖 发送文件读取任务到MCP客户端');

    const readTask = {
        task_type: "file_operation",
        command: {
            operation: "read",
            file_path: "README.md"
        },
        metadata: {
            priority: "normal",
            timeout: 300,
            test_type: "file_read_test"
        }
    };

    sendMCPTask(readTask, 'file_read');
}

function executeMCPFileWrite() {
    addDebugLogEntry('INFO', 'MCP_TEST', '✍️ 发送文件写入任务到MCP客户端');

    const writeTask = {
        task_type: "file_operation",
        command: {
            operation: "write",
            file_path: "debug_mcp_test_output.txt",
            content: `V4.5 MCP分离架构调试测试\n时间: ${new Date().toISOString()}\n测试类型: 调试界面批处理命令\n状态: 成功发送到挂起的MCP客户端`
        },
        metadata: {
            priority: "normal",
            timeout: 300,
            test_type: "file_write_test"
        }
    };

    sendMCPTask(writeTask, 'file_write');
}

// V4.5 事务测试函数
function executeTaskQueueStatus() {
    addDebugLogEntry('INFO', 'TRANSACTION', '📊 查询任务队列状态');

    fetch('/api/connected_clients')
        .then(response => response.json())
        .then(data => {
            addDebugLogEntry('INFO', 'TRANSACTION', `🔗 连接的MCP客户端: ${data.total_count}`);
            data.connected_clients.forEach(client => {
                addDebugLogEntry('INFO', 'TRANSACTION', `📱 客户端: ${client.client_id}`);
            });
        })
        .catch(error => {
            addDebugLogEntry('ERROR', 'TRANSACTION', `❌ 查询失败: ${error.message}`);
        });
}

function executeBatchTest() {
    addDebugLogEntry('INFO', 'BATCH_TEST', '🔄 启动批处理测试 - 连续发送3个任务');

    const tasks = [
        {
            task_type: "file_operation",
            command: { operation: "read", file_path: "README.md" },
            metadata: { test_type: "batch_test_1", priority: "normal" }
        },
        {
            task_type: "file_operation",
            command: {
                operation: "write",
                file_path: "batch_test_1.txt",
                content: "批处理测试文件1\n时间: " + new Date().toISOString()
            },
            metadata: { test_type: "batch_test_2", priority: "normal" }
        },
        {
            task_type: "code_analysis",
            command: {
                file_path: "tools/ace/src/four_layer_meeting_system/mcp_server/simple_ascii_launcher.py",
                analysis_type: "structure"
            },
            metadata: { test_type: "batch_test_3", priority: "normal" }
        }
    ];

    tasks.forEach((task, index) => {
        setTimeout(() => {
            addDebugLogEntry('INFO', 'BATCH_TEST', `📤 发送批处理任务 ${index + 1}/3`);
            sendMCPTask(task, `batch_${index + 1}`);
        }, index * 2000); // 每2秒发送一个任务
    });
}

function executeTransactionTest() {
    addDebugLogEntry('INFO', 'TRANSACTION_TEST', '🏗️ 启动事务测试 - 任务队列管理验证');

    // 第一阶段：发送任务
    const transactionTask = {
        task_type: "file_operation",
        command: {
            operation: "write",
            file_path: "transaction_test_output.txt",
            content: `事务测试文件\n开始时间: ${new Date().toISOString()}\n测试ID: transaction_${Date.now()}\n状态: 事务开始`
        },
        metadata: {
            test_type: "transaction_test",
            priority: "high",
            timeout: 300,
            transaction_id: `tx_${Date.now()}`
        }
    };

    addDebugLogEntry('INFO', 'TRANSACTION_TEST', '📤 发送事务任务...');

    // 🔧 修复：使用sendMCPTask动态选择客户端，而不是硬编码default_client
    sendMCPTask(transactionTask, 'transaction_test');
}

// V4.5 状态管理测试函数 - 修复：只调用API，不伪造debug_log
function executeClientStatesQuery() {
    // 删除伪造的前端debug_log，让服务器端真正的debug_log推送

    fetch('/api/client_states')
        .then(response => response.json())
        .then(data => {
            // 只处理错误情况，成功的调试信息由服务器端debug_log自动推送
            if (data.status !== 'success') {
                addDebugLogEntry('ERROR', 'STATE_MGMT', `❌ 查询失败: ${data.message}`);
            }
        })
        .catch(error => {
            addDebugLogEntry('ERROR', 'STATE_MGMT', `❌ 网络错误: ${error.message}`);
        });
}

function executeReconnectionHistoryQuery() {
    addDebugLogEntry('INFO', 'STATE_MGMT', '📈 查询重连历史记录');

    fetch('/api/reconnection_history')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                addDebugLogEntry('SUCCESS', 'STATE_MGMT', `✅ 重连历史查询成功，共${data.total_records}条记录`);

                Object.entries(data.reconnection_history).forEach(([clientId, record]) => {
                    const firstConnect = new Date(record.first_connected).toLocaleString();
                    const lastReconnect = new Date(record.last_reconnect).toLocaleString();
                    addDebugLogEntry('INFO', 'STATE_MGMT',
                        `🔄 ${clientId}: 首次连接${firstConnect}, 重连${record.reconnection_count}次, 最后重连${lastReconnect}`);
                });
            } else {
                addDebugLogEntry('ERROR', 'STATE_MGMT', `❌ 查询失败: ${data.message}`);
            }
        })
        .catch(error => {
            addDebugLogEntry('ERROR', 'STATE_MGMT', `❌ 网络错误: ${error.message}`);
        });
}

function sendMCPTask(taskData, testType) {
    // 首先获取可用的客户端列表
    fetch('/api/client_states')
        .then(response => response.json())
        .then(clientData => {
            if (clientData.status === 'success' && clientData.total_clients > 0) {
                // 选择第一个可用的客户端
                const availableClients = Object.keys(clientData.client_states);
                const selectedClient = availableClients[0];

                addDebugLogEntry('INFO', 'MCP_API', `📡 选择客户端: ${selectedClient}`);

                // 添加client_id到任务数据
                const taskWithClient = {
                    ...taskData,
                    client_id: selectedClient
                };

                // 发送任务
                fetch('/api/send_task', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(taskWithClient)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        addDebugLogEntry('SUCCESS', 'MCP_API', `✅ 任务发送成功 (${testType}): ${data.task_id}`);
                    } else {
                        addDebugLogEntry('ERROR', 'MCP_API', `❌ 任务发送失败 (${testType}): ${data.message}`);
                    }
                })
                .catch(error => {
                    addDebugLogEntry('ERROR', 'MCP_API', `❌ 网络错误 (${testType}): ${error.message}`);
                });
            } else {
                addDebugLogEntry('ERROR', 'MCP_API', `❌ 没有可用的MCP客户端 (${testType})`);
            }
        })
        .catch(error => {
            addDebugLogEntry('ERROR', 'MCP_API', `❌ 获取客户端列表失败 (${testType}): ${error.message}`);
        });
}

function addDebugLogEntry(level, source, message) {
    const logContainer = document.getElementById('debug-log-container');
    const logEntry = document.createElement('div');
    logEntry.className = 'debug-log-entry';

    const now = new Date().toISOString();
    logEntry.innerHTML = `
        <span class="debug-timestamp">${now}</span>
        <span class="debug-level ${level.toLowerCase()}">${level}</span>
        <span class="debug-source">${source}</span>
        <span class="debug-message">${message}</span>
    `;

    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
}

// Socket.IO调试事件监听
socket.on('debug_response', function(data) {
    addDebugLogEntry('INFO', 'SOCKET', '收到调试响应');
    console.log('Debug response:', data); // 这里可以使用console，因为是调试中心
});

socket.on('debug_error', function(data) {
    addDebugLogEntry('ERROR', 'SOCKET', `调试错误: ${data.message}`);
});

// 实时调试日志推送监听 - 核心功能
socket.on('debug_log_update', function(data) {
    // 直接添加服务器推送的调试日志到界面
    addDebugLogEntry(data.level, data.source, data.message);

    // CRITICAL级别特殊处理
    if (data.level === 'CRITICAL') {
        // 添加闪烁效果
        const logEntries = document.querySelectorAll('.debug-log-entry');
        const lastEntry = logEntries[logEntries.length - 1];
        if (lastEntry) {
            lastEntry.classList.add('CRITICAL');
            lastEntry.style.animation = 'blink 1s infinite';
        }
    }
});

// V4.5 新功能测试函数 - 文档编辑器
function executeDocumentEditTest() {
    addDebugLogEntry('INFO', 'DOCUMENT_EDIT', '📝 测试V4.5极简文档编辑器功能');

    const documentEditTask = {
        task_type: "document_edit",
        command: {
            file_path: "test_document_editor.md",
            operation: "insert_line",
            parameters: {
                line_number: 1,
                content: "# V4.5文档编辑器测试成功！",
                position: "after"
            }
        },
        metadata: {
            test_type: "document_edit_test",
            priority: "normal",
            timeout: 300
        }
    };

    addDebugLogEntry('INFO', 'DOCUMENT_EDIT', '📤 发送文档编辑任务...');
    sendMCPTask(documentEditTask, 'document_edit_test');
}

// V4.5 新功能测试函数 - read_line操作
function executeReadLineTest() {
    addDebugLogEntry('INFO', 'READ_LINE', '📖 测试V4.5文档编辑器read_line功能');

    const readLineTask = {
        task_type: "document_edit",
        command: {
            file_path: "test_document_editor.md",
            operation: "read_line",
            parameters: {
                line_number: 1
            }
        },
        metadata: {
            test_type: "read_line_test",
            priority: "normal",
            timeout: 300
        }
    };

    addDebugLogEntry('INFO', 'READ_LINE', '📤 发送read_line任务...');
    sendMCPTask(readLineTask, 'read_line_test');
}

// V4.5 新功能测试函数 - update_line操作
function executeUpdateLineTest() {
    addDebugLogEntry('INFO', 'UPDATE_LINE', '✏️ 测试V4.5文档编辑器update_line功能');

    const updateLineTask = {
        task_type: "document_edit",
        command: {
            file_path: "test_document_editor.md",
            operation: "update_line",
            parameters: {
                line_number: 1,
                content: "# V4.5文档编辑器update_line测试成功！"
            }
        },
        metadata: {
            test_type: "update_line_test",
            priority: "normal",
            timeout: 300
        }
    };

    addDebugLogEntry('INFO', 'UPDATE_LINE', '📤 发送update_line任务...');
    sendMCPTask(updateLineTask, 'update_line_test');
}

// V4.5 新功能测试函数 - delete_line操作
function executeDeleteLineTest() {
    addDebugLogEntry('INFO', 'DELETE_LINE', '🗑️ 测试V4.5文档编辑器delete_line功能');

    const deleteLineTask = {
        task_type: "document_edit",
        command: {
            file_path: "test_document_editor.md",
            operation: "delete_line",
            parameters: {
                line_number: 1
            }
        },
        metadata: {
            test_type: "delete_line_test",
            priority: "normal",
            timeout: 300
        }
    };

    addDebugLogEntry('INFO', 'DELETE_LINE', '📤 发送delete_line任务...');
    sendMCPTask(deleteLineTask, 'delete_line_test');
}

// V4.5 新功能测试函数 - replace_all操作
function executeReplaceAllTest() {
    addDebugLogEntry('INFO', 'REPLACE_ALL', '🔄 测试V4.5文档编辑器replace_all功能');

    const replaceAllTask = {
        task_type: "document_edit",
        command: {
            file_path: "test_document_editor.md",
            operation: "replace_all",
            parameters: {
                search_pattern: "V4.5文档编辑器测试成功！",
                replace_with: "V4.5文档编辑器replace_all测试成功！",
                regex: false,
                case_sensitive: true
            }
        },
        metadata: {
            test_type: "replace_all_test",
            priority: "normal",
            timeout: 300
        }
    };

    addDebugLogEntry('INFO', 'REPLACE_ALL', '📤 发送replace_all任务...');
    sendMCPTask(replaceAllTask, 'replace_all_test');
}

// V4.5 新功能测试函数 - 复杂目录删除操作测试
function executeDirectoryOperationTest() {
    addDebugLogEntry('INFO', 'DIRECTORY_OP', '🗑️ 测试delete_directory功能 - 创建复杂目录结构并删除');

    // 步骤1：创建主测试目录
    const createMainDirTask = {
        task_type: "directory_operation",
        command: {
            operation: "create_directory",
            parameters: {
                directory_path: "temp_complex_test_dir",
                recursive: true
            }
        },
        metadata: {
            test_type: "directory_operation_test",
            priority: "normal",
            timeout: 300
        }
    };

    addDebugLogEntry('INFO', 'DIRECTORY_OP', '📤 步骤1：创建主测试目录 temp_complex_test_dir...');
    sendMCPTask(createMainDirTask, 'directory_operation_test');

    // 步骤2：在主目录中创建文件
    setTimeout(() => {
        const createFileTask = {
            task_type: "document_edit",
            command: {
                operation: "insert_line",
                parameters: {
                    file_path: "temp_complex_test_dir/test_file.txt",
                    line_number: 1,
                    content: "这是测试目录中的文件内容"
                }
            },
            metadata: {
                test_type: "directory_operation_test",
                priority: "normal",
                timeout: 300
            }
        };

        addDebugLogEntry('INFO', 'DIRECTORY_OP', '📤 步骤2：在主目录中创建文件 test_file.txt...');
        sendMCPTask(createFileTask, 'directory_operation_test');
    }, 2000);

    // 步骤3：创建子目录
    setTimeout(() => {
        const createSubDirTask = {
            task_type: "directory_operation",
            command: {
                operation: "create_directory",
                parameters: {
                    directory_path: "temp_complex_test_dir/sub_directory",
                    recursive: true
                }
            },
            metadata: {
                test_type: "directory_operation_test",
                priority: "normal",
                timeout: 300
            }
        };

        addDebugLogEntry('INFO', 'DIRECTORY_OP', '📤 步骤3：创建子目录 sub_directory...');
        sendMCPTask(createSubDirTask, 'directory_operation_test');
    }, 4000);

    // 步骤4：在子目录中创建文件
    setTimeout(() => {
        const createSubFileTask = {
            task_type: "document_edit",
            command: {
                operation: "insert_line",
                parameters: {
                    file_path: "temp_complex_test_dir/sub_directory/sub_file.txt",
                    line_number: 1,
                    content: "这是子目录中的文件内容"
                }
            },
            metadata: {
                test_type: "directory_operation_test",
                priority: "normal",
                timeout: 300
            }
        };

        addDebugLogEntry('INFO', 'DIRECTORY_OP', '📤 步骤4：在子目录中创建文件 sub_file.txt...');
        sendMCPTask(createSubFileTask, 'directory_operation_test');
    }, 6000);

    // 步骤5：尝试删除整个复杂目录结构
    setTimeout(() => {
        const deleteComplexDirTask = {
            task_type: "directory_operation",
            command: {
                operation: "delete_directory",
                parameters: {
                    directory_path: "temp_complex_test_dir",
                    recursive: true,
                    force: false
                }
            },
            metadata: {
                test_type: "directory_operation_test",
                priority: "normal",
                timeout: 300
            }
        };

        addDebugLogEntry('INFO', 'DIRECTORY_OP', '📤 步骤5：删除复杂目录结构 temp_complex_test_dir（包含文件和子目录）...');
        sendMCPTask(deleteComplexDirTask, 'directory_operation_test');
    }, 8000);

    // 步骤6：验证复杂目录是否真的被删除
    setTimeout(() => {
        const verifyComplexDirTask = {
            task_type: "directory_operation",
            command: {
                operation: "list_directory",
                parameters: {
                    directory_path: ".",
                    recursive: false,
                    include_files: false,
                    include_dirs: true,
                    max_depth: 1
                }
            },
            metadata: {
                test_type: "directory_operation_test",
                priority: "normal",
                timeout: 300
            }
        };

        addDebugLogEntry('INFO', 'DIRECTORY_OP', '📤 步骤6：验证删除结果 - 检查temp_complex_test_dir是否还存在...');
        sendMCPTask(verifyComplexDirTask, 'directory_operation_test');
    }, 10000);
}

// V4.5 新功能测试函数 - copy_file操作
function executeCopyFileTest() {
    addDebugLogEntry('INFO', 'COPY_FILE', '📋 测试V4.5文件复制功能');

    const copyFileTask = {
        task_type: "directory_operation",
        command: {
            operation: "copy_file",
            parameters: {
                source_path: "tools/ace/src/four_layer_meeting_system/mcp_server/test_data/test_document.md",
                target_path: "tools/ace/src/four_layer_meeting_system/mcp_server/test_data/test_document_copy.md",
                overwrite: false
            }
        },
        metadata: {
            test_type: "copy_file_test",
            priority: "normal",
            timeout: 300
        }
    };

    addDebugLogEntry('INFO', 'COPY_FILE', '📤 发送文件复制任务...');
    sendMCPTask(copyFileTask, 'copy_file_test');
}

// V4.5 新功能测试函数 - 搜索文件
function executeSearchFilesTest() {
    addDebugLogEntry('INFO', 'SEARCH_FILES', '🔍 测试search_files功能');

    const searchTask = {
        task_type: "directory_operation",
        command: {
            operation: "search_files",
            parameters: {
                directory_path: "tools/",
                pattern: "*.md",
                recursive: true,
                max_results: 10
            }
        },
        metadata: {
            test_type: "search_files_test",
            priority: "normal",
            timeout: 300
        }
    };

    addDebugLogEntry('INFO', 'SEARCH_FILES', '📤 发送搜索文件任务...');
    sendMCPTask(searchTask, 'search_files_test');
}

// V4.5 新功能测试函数 - 删除操作测试
function executeDeleteTest() {
    addDebugLogEntry('INFO', 'DELETE_TEST', '🗑️ 测试删除操作功能');

    // 首先创建测试文件和目录
    const createTask = {
        task_type: "document_edit",
        command: {
            operation: "insert_line",
            parameters: {
                file_path: "tools/test_delete_file.txt",
                line_number: 1,
                content: "这是用于删除测试的文件"
            }
        },
        metadata: {
            test_type: "delete_test_setup",
            priority: "normal",
            timeout: 300
        }
    };

    addDebugLogEntry('INFO', 'DELETE_TEST', '📤 创建测试文件...');
    sendMCPTask(createTask, 'delete_test_setup');

    // 延迟执行删除测试
    setTimeout(() => {
        const deleteTask = {
            task_type: "directory_operation",
            command: {
                operation: "delete_file",
                parameters: {
                    file_path: "tools/test_delete_file.txt"
                }
            },
            metadata: {
                test_type: "delete_test",
                priority: "normal",
                timeout: 300
            }
        };

        addDebugLogEntry('INFO', 'DELETE_TEST', '📤 发送删除文件任务...');
        sendMCPTask(deleteTask, 'delete_test');
    }, 2000);
}

// V45失败API专项测试函数 - 精确复现V45失败用例
function executeFailedApiTest() {
    addDebugLogEntry('INFO', 'FAILED_API_TEST', '🔧 V45失败API专项测试 - 精确复现失败用例');

    // 创建测试文件用于后续测试
    const createTestFileTask = {
        task_type: "document_edit",
        command: {
            operation: "insert_line",
            parameters: {
                file_path: "temp_delete_with_backup.txt",
                line_number: 1,
                content: "这是用于删除测试的文件内容(带备份)"
            }
        },
        metadata: {
            test_type: "failed_api_test",
            priority: "normal",
            timeout: 300
        }
    };

    addDebugLogEntry('INFO', 'FAILED_API_TEST', '📤 步骤1：创建测试文件 temp_delete_with_backup.txt...');
    sendMCPTask(createTestFileTask, 'failed_api_test');

    // 创建第二个测试文件
    setTimeout(() => {
        const createTestFile2Task = {
            task_type: "document_edit",
            command: {
                operation: "insert_line",
                parameters: {
                    file_path: "temp_delete_no_backup.txt",
                    line_number: 1,
                    content: "这是用于删除测试的文件内容(不备份)"
                }
            },
            metadata: {
                test_type: "failed_api_test",
                priority: "normal",
                timeout: 300
            }
        };

        addDebugLogEntry('INFO', 'FAILED_API_TEST', '📤 步骤2：创建测试文件 temp_delete_no_backup.txt...');
        sendMCPTask(createTestFile2Task, 'failed_api_test');
    }, 2000);

    // V45失败用例1：delete_file "删除存在的文件(带备份)"
    setTimeout(() => {
        const deleteFileWithBackupTask = {
            task_type: "directory_operation",
            command: {
                operation: "delete_file",
                parameters: {
                    file_path: "temp_delete_with_backup.txt",
                    create_backup: true
                }
            },
            metadata: {
                test_type: "failed_api_test",
                priority: "normal",
                timeout: 300
            }
        };

        addDebugLogEntry('INFO', 'FAILED_API_TEST', '📤 步骤3：V45失败用例 - delete_file(带备份)...');
        sendMCPTask(deleteFileWithBackupTask, 'failed_api_test');
    }, 4000);

    // V45失败用例2：delete_file "删除存在的文件(不备份)"
    setTimeout(() => {
        const deleteFileNoBackupTask = {
            task_type: "directory_operation",
            command: {
                operation: "delete_file",
                parameters: {
                    file_path: "temp_delete_no_backup.txt",
                    create_backup: false
                }
            },
            metadata: {
                test_type: "failed_api_test",
                priority: "normal",
                timeout: 300
            }
        };

        addDebugLogEntry('INFO', 'FAILED_API_TEST', '📤 步骤4：V45失败用例 - delete_file(不备份)...');
        sendMCPTask(deleteFileNoBackupTask, 'failed_api_test');
    }, 6000);

    // V45失败用例3：insert_line "插入到行后"
    setTimeout(() => {
        const insertLineAfterTask = {
            task_type: "document_edit",
            command: {
                operation: "insert_line",
                parameters: {
                    file_path: "test_doc.md",
                    line_number: 1,
                    content: "测试内容",
                    position: "after"
                }
            },
            metadata: {
                test_type: "failed_api_test",
                priority: "normal",
                timeout: 300
            }
        };

        addDebugLogEntry('INFO', 'FAILED_API_TEST', '📤 步骤5：V45失败用例 - insert_line(插入到行后)...');
        sendMCPTask(insertLineAfterTask, 'failed_api_test');
    }, 8000);

    // V45失败用例4：insert_line "插入特殊字符"
    setTimeout(() => {
        const insertLineSpecialTask = {
            task_type: "document_edit",
            command: {
                operation: "insert_line",
                parameters: {
                    file_path: "test_doc.md",
                    line_number: 1,
                    content: "中文🎉特殊字符",
                    position: "after"
                }
            },
            metadata: {
                test_type: "failed_api_test",
                priority: "normal",
                timeout: 300
            }
        };

        addDebugLogEntry('INFO', 'FAILED_API_TEST', '📤 步骤6：V45失败用例 - insert_line(插入特殊字符)...');
        sendMCPTask(insertLineSpecialTask, 'failed_api_test');
    }, 10000);

    // V45失败用例5：delete_line "删除超出范围"
    setTimeout(() => {
        const deleteLineOutOfRangeTask = {
            task_type: "document_edit",
            command: {
                operation: "delete_line",
                parameters: {
                    file_path: "test_doc.md",
                    line_number: 999,
                    count: 1
                }
            },
            metadata: {
                test_type: "failed_api_test",
                priority: "normal",
                timeout: 300
            }
        };

        addDebugLogEntry('INFO', 'FAILED_API_TEST', '📤 步骤7：V45失败用例 - delete_line(删除超出范围)...');
        sendMCPTask(deleteLineOutOfRangeTask, 'failed_api_test');
    }, 12000);

    // 验证测试结果
    setTimeout(() => {
        const verifyTask = {
            task_type: "directory_operation",
            command: {
                operation: "list_directory",
                parameters: {
                    directory_path: ".",
                    recursive: false,
                    include_files: true,
                    include_dirs: false,
                    max_depth: 1
                }
            },
            metadata: {
                test_type: "failed_api_test",
                priority: "normal",
                timeout: 300
            }
        };

        addDebugLogEntry('INFO', 'FAILED_API_TEST', '📤 步骤8：验证所有V45失败用例的测试结果...');
        sendMCPTask(verifyTask, 'failed_api_test');
    }, 14000);
}

// V45抽象层一键测试函数
function executeV45AbstractionTest() {
    addDebugLogEntry('INFO', 'V45_TEST', '🧪 启动V45抽象层一键测试 - 三重验证所有27个API');

    // 发送V45抽象层测试请求
    fetch('/api/test_v45_abstraction', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            client_id: null  // 自动选择可用客户端
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'completed') {
            const summary = data.test_summary;
            const successRate = summary.success_rate.toFixed(1);

            addDebugLogEntry('SUCCESS', 'V45_TEST', `✅ V45抽象层测试完成！成功率: ${successRate}%`);
            addDebugLogEntry('INFO', 'V45_TEST', `📊 测试统计: ${summary.passed_tests}/${summary.total_tests} 个API测试通过`);
            addDebugLogEntry('INFO', 'V45_TEST', `⏱️ 测试耗时: ${summary.test_duration.toFixed(2)}秒`);
            addDebugLogEntry('INFO', 'V45_TEST', `🎯 测试结论: ${data.conclusion}`);

            // 显示API分解统计
            Object.entries(data.api_breakdown).forEach(([apiName, stats]) => {
                const apiSuccessRate = stats.success_rate.toFixed(1);
                addDebugLogEntry('DEBUG', 'V45_TEST', `   📋 ${apiName}: ${stats.success}/${stats.total} (${apiSuccessRate}%)`);
            });

        } else {
            addDebugLogEntry('ERROR', 'V45_TEST', `❌ V45抽象层测试失败: ${data.message}`);
        }
    })
    .catch(error => {
        addDebugLogEntry('ERROR', 'V45_TEST', `❌ V45抽象层测试网络错误: ${error.message}`);
    });
}

// 命令输入回车执行
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('debug-command-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            executeDebugCommand();
        }
    });

    // 删除假的调试信息 - 让服务器端真正的debug_log推送
});
</script>
{% endblock %}
