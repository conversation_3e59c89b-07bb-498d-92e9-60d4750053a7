# -*- coding: utf-8 -*-
"""
项目经理核心类 - 负责驱动单一项目的完整治理流程
"""

from datetime import datetime
import uuid
import threading
import os
from typing import Optional, Callable, List, Dict, Any
from ..data_models.status_models import ProjectStatus, ReviewStageInfo, ReviewStageStatus
from ..blueprints.bp_admission_review import AdmissionReviewBlueprint, ReviewStage, StageValidator

class ProjectManager:
    """
    项目经理实例类。
    每个实例与一个特定的项目路径绑定，并独立负责该项目的治理生命周期。
    
    核心职责：
    1. 配置和管理审查流程
    2. 执行项目治理任务
    3. 维护项目状态
    4. 与前端实时通信
    """
    def __init__(self, design_doc_path: str):
        # 使用确定性哈希算法确保跨会话一致性（修复Python hash()随机化问题）
        import hashlib
        import os
        normalized_path = os.path.normpath(design_doc_path)
        path_bytes = normalized_path.encode('utf-8')
        path_hash_full = hashlib.sha256(path_bytes).hexdigest()
        # 取前8位十六进制作为ID，确保确定性和唯一性
        path_hash = int(path_hash_full[:8], 16)
        self.id = f"pm_{path_hash:08x}"  # 8位十六进制
        self.design_doc_path = design_doc_path
        self.state = 'initialized'
        self.created_at = datetime.now().isoformat()
        self.last_updated = self.created_at
        
        # 工作区路径
        self.workspace_path = os.path.join(design_doc_path, "_pm_workspace")
        self.status_file_path = os.path.join(self.workspace_path, "status.json")
        
        # 状态管理 - 直接使用 ProjectStatus
        self.status: Optional[ProjectStatus] = None
        
        # 状态回调（HTTP轮询架构）
        self.status_callback: Optional[Callable] = None
        
        # 审查蓝图 - 由项目经理配置和管理
        self.admission_review: Optional[AdmissionReviewBlueprint] = None
        
        print(f"✅ ProjectManager instance created with ID: {self.id} for path: {self.design_doc_path}")
        print(f"🔧 ID生成算法: SHA-256确定性哈希（修复Python hash()随机化问题）")

    def _initialize_workspace(self):
        """初始化工作区 - 简化版本"""
        try:
            # 创建工作区目录
            os.makedirs(self.workspace_path, exist_ok=True)
            
            # 创建或加载状态文件
            if not os.path.exists(self.status_file_path):
                # 创建初始状态 - 不再传递路径信息
                self.status = ProjectStatus(
                    id=self.id
                )
                self.status.save_to_file(self.status_file_path)
                print(f"📁 工作区初始化完成: {self.workspace_path}")
                print(f"📄 状态文件创建: {self.status_file_path}")
                print(f"🔧 审查流程: 待配置（0个阶段）")
            else:
                # 加载现有状态
                try:
                    self.status = ProjectStatus.load_from_file(self.status_file_path)
                    print(f"📁 工作区已存在，加载状态: {self.workspace_path}")
                except Exception as e:
                    print(f"⚠️ 状态文件损坏，重新创建: {e}")
                    self.status = ProjectStatus(
                        id=self.id
                    )
                    self.status.save_to_file(self.status_file_path)
            
        except Exception as e:
            print(f"❌ 工作区初始化失败: {str(e)}")
            raise Exception(f"工作区初始化失败: {str(e)}")

    def save_status(self):
        """保存状态到文件"""
        if self.status:
            try:
                self.status.save_to_file(self.status_file_path)
            except Exception as e:
                print(f"❌ 保存状态失败: {str(e)}")
                raise

    def set_status_callback(self, callback: Callable):
        """设置状态更新回调函数（HTTP轮询架构）"""
        self.status_callback = callback
        print(f"✅ ProjectManager {self.id} 状态回调已设置")

    def _update_status(self, status_data: dict):
        """更新状态并通知回调"""
        if self.status_callback:
            self.status_callback(self.id, status_data)
            print(f"🔄 状态更新已推送: {status_data.get('message', 'Unknown')}")

    # 保留兼容性方法
    def set_socketio(self, callback: Callable):
        """设置WebSocket回调函数（已弃用，保留兼容性）"""
        print("⚠️ set_socketio已弃用，请使用set_status_callback")
        self.set_status_callback(callback)

    def configure_review_pipeline(self, stages_config: Optional[List[ReviewStage]] = None):
        """
        配置审查流程 - 项目经理的核心职责
        
        Args:
            stages_config: 自定义的审查阶段配置，如果为None则使用默认配置
        """
        # 创建审查蓝图并配置阶段
        self.admission_review = AdmissionReviewBlueprint(stages_config)
        
        # 更新状态中的审查进度信息
        if self.status and self.status.review_progress:
            self._update_review_progress_from_blueprint()
        
        # 保存状态
        self.save_status()
        
        print(f"🔧 项目经理 {self.id} 已配置审查流程，共 {len(self.admission_review.get_stages())} 个阶段")

    def _update_review_progress_from_blueprint(self):
        """从审查蓝图更新状态中的审查进度信息"""
        if not self.admission_review or not self.status:
            return
        
        # 获取蓝图中的阶段配置
        stage_configs = self.admission_review.get_stage_config()
        
        # 更新状态中的阶段信息
        stages = []
        for config in stage_configs:
            stage = ReviewStageInfo(
                name=config["name"],
                description=config["description"],
                order=config["order"],
                status=ReviewStageStatus.PENDING,
                is_blocking=config["is_blocking"]
            )
            stages.append(stage)
        
        if self.status.review_progress:
            self.status.review_progress.stages = stages
            self.status.review_progress.total_stages = len(stages)
            self.status.review_progress.completed_stages = 0
            self.status.review_progress.current_stage = None

    def add_review_stage(self, stage: ReviewStage, validator: Optional[StageValidator] = None):
        """
        动态添加审查阶段 - 项目经理的扩展能力
        
        Args:
            stage: 审查阶段定义
            validator: 可选的验证器实例
            
        Raises:
            Exception: 如果审查流程未配置
        """
        # ✅ 检查是否已配置审查流程
        if not self.admission_review:
            raise Exception("审查流程未配置，请先调用 configure_review_pipeline() 配置审查阶段")
        
        self.admission_review.add_stage(stage, validator)
        self._update_review_progress_from_blueprint()
        
        # 保存状态
        self.save_status()
        
        print(f"➕ 项目经理 {self.id} 添加审查阶段: {stage.name}")

    def remove_review_stage(self, stage_name: str):
        """
        移除审查阶段 - 项目经理的管理能力
        
        Args:
            stage_name: 要移除的阶段名称
        """
        if not self.admission_review:
            return
        
        self.admission_review.remove_stage(stage_name)
        self._update_review_progress_from_blueprint()
        
        # 保存状态
        self.save_status()
        
        print(f"➖ 项目经理 {self.id} 移除审查阶段: {stage_name}")

    def get_review_stages(self) -> List[ReviewStage]:
        """获取当前配置的审查阶段"""
        if not self.admission_review:
            return []
        return self.admission_review.get_stages()

    def run_admission_review(self) -> bool:
        """
        执行项目准入审查 - 项目经理的核心业务逻辑
        
        Returns:
            bool: 审查是否成功
            
        Raises:
            Exception: 如果审查流程未配置
        """
        # ✅ 检查是否已配置审查流程
        if not self.admission_review:
            raise Exception("审查流程未配置，请先调用 configure_review_pipeline() 配置审查阶段")
        
        if not self.status:
            raise Exception("项目状态未初始化")
        
        # 更新状态为审查中
        self.status.start_review()
        self.save_status()
        
        # 设置进度回调
        def progress_callback(event_type: str, data: Dict[str, Any]):
            """审查进度回调函数"""
            if event_type == "stage_started":
                self._handle_stage_started(data)
            elif event_type == "stage_completed":
                self._handle_stage_completed(data)
            elif event_type == "review_failed":
                self._handle_review_failed(data)
            elif event_type == "review_completed":
                self._handle_review_completed(data)
            
            # 通过WebSocket推送进度
            if self.socketio_callback:
                self.socketio_callback(event_type, data)
        
        try:
            # 执行审查流程
            success, results = self.admission_review.run_review(
                self.design_doc_path, 
                progress_callback
            )
            
            # 更新最终状态
            if success:
                self.status.complete_review(success=True)
            else:
                error_message = results[-1].message if results else "审查失败"
                self.status.complete_review(success=False, error_message=error_message)
            
            # 保存状态
            self.save_status()
            
            return success
            
        except Exception as e:
            # 处理异常
            self.status.complete_review(success=False, error_message=str(e))
            self.save_status()
            raise

    def _handle_stage_started(self, data: Dict[str, Any]):
        """处理阶段开始事件"""
        stage_name = data.get("stage_name")
        if self.status and self.status.review_progress:
            self.status.update_stage_progress(
                stage_name, 
                ReviewStageStatus.RUNNING,
                f"开始执行 {stage_name}"
            )
            self.save_status()

    def _handle_stage_completed(self, data: Dict[str, Any]):
        """处理阶段完成事件"""
        stage_name = data.get("stage_name")
        result = data.get("result", {})
        
        if self.status and self.status.review_progress:
            status = ReviewStageStatus.COMPLETED if not result.get("is_blocking") else ReviewStageStatus.FAILED
            self.status.update_stage_progress(
                stage_name, 
                status,
                result.get("message", f"{stage_name} 完成")
            )
            self.save_status()

    def _handle_review_failed(self, data: Dict[str, Any]):
        """处理审查失败事件"""
        failed_stage = data.get("failed_stage")
        message = data.get("message", "审查失败")
        
        if self.status and self.status.review_progress:
            self.status.update_stage_progress(
                failed_stage,
                ReviewStageStatus.FAILED,
                message
            )
            self.save_status()

    def _handle_review_completed(self, data: Dict[str, Any]):
        """处理审查完成事件"""
        if self.status and self.status.review_progress:
            # 所有阶段都已完成
            for stage in self.status.review_progress.stages:
                if stage.status == ReviewStageStatus.PENDING:
                    stage.status = ReviewStageStatus.COMPLETED
                    stage.end_time = datetime.now().isoformat()
            self.save_status()

    def get_status(self):
        """
        获取当前项目经理实例的状态。
        """
        if self.status:
            return self.status.to_dict()
        return None

    def get_review_progress(self):
        """
        获取审查进度信息。
        """
        if self.status and self.status.review_progress:
            return {
                "current_stage": self.status.review_progress.current_stage,
                "completed_stages": self.status.review_progress.completed_stages,
                "total_stages": self.status.review_progress.total_stages,
                "overall_status": self.status.review_progress.overall_status.value,
                "stages": [
                    {
                        "name": stage.name,
                        "description": stage.description,
                        "order": stage.order,
                        "status": stage.status.value,
                        "start_time": stage.start_time,
                        "end_time": stage.end_time,
                        "message": stage.message,
                        "is_blocking": stage.is_blocking
                    }
                    for stage in self.status.review_progress.stages
                ]
            }
        return None

    def run_admission_review(self):
        """
        执行项目准入审查流程（HTTP轮询架构）
        模拟四阶段阻塞式验证流程
        """
        import time

        print(f"🚀 开始执行项目准入审查，task_id: {self.id}")

        # 阶段1：基础资产盘点
        self._update_status({
            'status': 'reviewing',
            'stage': 1,
            'message': '正在进行基础资产盘点...',
            'progress_percentage': 25
        })
        time.sleep(3)  # 模拟处理时间

        # 阶段2：核心模块语法验证
        self._update_status({
            'status': 'reviewing',
            'stage': 2,
            'message': '正在进行核心模块语法验证...',
            'progress_percentage': 50
        })
        time.sleep(3)

        # 阶段3：宏观架构健康度评估
        self._update_status({
            'status': 'reviewing',
            'stage': 3,
            'message': '正在进行宏观架构健康度评估...',
            'progress_percentage': 75
        })
        time.sleep(3)

        # 阶段4：设计完备性与一致性审计
        self._update_status({
            'status': 'reviewing',
            'stage': 4,
            'message': '正在进行设计完备性与一致性审计...',
            'progress_percentage': 90
        })
        time.sleep(2)

        # 完成
        self._update_status({
            'status': 'completed',
            'stage': 4,
            'message': '项目准入审查已完成，所有阶段验证通过',
            'progress_percentage': 100,
            'completed_at': datetime.now().isoformat()
        })

        print(f"✅ 项目准入审查完成，task_id: {self.id}")
