# -*- coding: utf-8 -*-
"""
状态队列管理器 - 管理task_id对应的状态数据队列
用于HTTP轮询架构的状态同步
"""

import threading
from datetime import datetime
from typing import Dict, Optional, Any


class StatusQueueManager:
    """管理task_id对应的状态数据队列"""
    
    def __init__(self):
        self.status_data: Dict[str, Dict[str, Any]] = {}  # task_id -> status_dict
        self.lock = threading.Lock()
        print("✅ StatusQueueManager initialized")
    
    def initialize_task(self, task_id: str, initial_data: dict) -> None:
        """
        初始化任务状态
        
        Args:
            task_id: 任务ID
            initial_data: 初始状态数据
        """
        with self.lock:
            self.status_data[task_id] = {
                **initial_data,
                'last_updated': datetime.now().isoformat(),
                'created_at': datetime.now().isoformat()
            }
            print(f"📋 Task initialized: {task_id}")
    
    def update_status(self, task_id: str, update_data: dict) -> None:
        """
        更新任务状态（供ProjectManager回调）
        
        Args:
            task_id: 任务ID
            update_data: 更新的状态数据
        """
        with self.lock:
            if task_id in self.status_data:
                self.status_data[task_id].update(update_data)
                self.status_data[task_id]['last_updated'] = datetime.now().isoformat()
                print(f"🔄 Status updated for task: {task_id}")
            else:
                print(f"⚠️ Task not found for status update: {task_id}")
    
    def get_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态（供轮询接口调用）
        
        Args:
            task_id: 任务ID
            
        Returns:
            状态数据字典，如果任务不存在则返回None
        """
        with self.lock:
            return self.status_data.get(task_id, None)
    
    def remove_task(self, task_id: str) -> bool:
        """
        移除任务状态数据
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功移除
        """
        with self.lock:
            if task_id in self.status_data:
                del self.status_data[task_id]
                print(f"🗑️ Task removed: {task_id}")
                return True
            return False
    
    def list_active_tasks(self) -> Dict[str, Dict[str, Any]]:
        """
        列出所有活跃任务
        
        Returns:
            所有任务的状态数据
        """
        with self.lock:
            return self.status_data.copy()
    
    def get_task_count(self) -> int:
        """
        获取活跃任务数量
        
        Returns:
            任务数量
        """
        with self.lock:
            return len(self.status_data)
    
    def cleanup_old_tasks(self, max_age_hours: int = 24) -> int:
        """
        清理超过指定时间的旧任务
        
        Args:
            max_age_hours: 最大保留时间（小时）
            
        Returns:
            清理的任务数量
        """
        from datetime import timedelta
        
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        removed_count = 0
        
        with self.lock:
            tasks_to_remove = []
            for task_id, data in self.status_data.items():
                created_at = datetime.fromisoformat(data.get('created_at', ''))
                if created_at < cutoff_time:
                    tasks_to_remove.append(task_id)
            
            for task_id in tasks_to_remove:
                del self.status_data[task_id]
                removed_count += 1
            
            if removed_count > 0:
                print(f"🧹 Cleaned up {removed_count} old tasks")
        
        return removed_count
