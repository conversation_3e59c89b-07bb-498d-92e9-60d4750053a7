# 系统模式 (System Patterns) - V4.2

*此文档记录了项目中的关键技术决策、设计模式和架构原则。本文档已根据V4.2架构进行全面更新，其核心是“统一语义模型”和“插件化架构”。*

## 核心架构原则 (V4.2)

### 1. 统一语义模型原则 (Unified Semantic Model) - **核心原则**
系统的所有知识，无论是通用约束、边界条件、状态机还是未来任何新的关键要素，都**必须**通过一个统一的、可扩展的`AtomicConstraint`数据模型来承载。系统的扩展性通过为该模型定义新的`category`来实现，而非通过创建新的数据模型。这确保了系统的核心数据结构保持绝对的稳定与一致。

### 2. 两阶段知识提炼管道原则 (Two-Stage Knowledge Pipeline)
为了确保概念清晰，从原始设计文档到结构化知识的转化，**必须**遵循严格的两阶段管道：
- **第一阶段：意图分类 (Intent Classification)**: 在“宏观语义地图构建”流程中，根据《黄金准则》，将原始文本的**目的**分类为`约束`、`护栏`、`上下文`或`正文`。
- **第二阶段：实体分类 (Entity Classification)**: 在“阶段零”中，接收带有“意图”标签的文本，并将其描述的**技术实体**分类为一个带有明确`category`的`AtomicConstraint`对象。
这两个阶段职责单一且不可逾越，确保了从模糊意图到精确实体的有序转化。

### 3. 声明式的引用式治理 (Declarative, Referential Governance)
*(与V4.1保持一致)* 系统的治理和知识传递，是通过声明式的引用机制完成的。下游模块通过引用上游预定义的、拥有唯一ID的`AtomicConstraint`来表达其遵从性。当需要对上层知识进行细化时，则通过创建新的、带有`parent_id`的本地`AtomicConstraint`来实现“分叉”。

## 核心设计模式 (V4.2)

### 1. “微核 + 插件式验证器”模式 (Microkernel + Pluggable Validators) - **V4.2核心模式**
这是V4.2架构在执行阶段的核心模式，旨在为AI开发者提供一个高度解耦、可独立调试、易于扩展的开发框架。
- **问题**: 如何在支持日益丰富的语义类型（`category`）的同时，保持核心验证引擎的稳定和简洁？
- **V4.2解决方案**:
    1.  **微核 (`ValidationLoop`)**: 核心的`ValidationLoop`引擎本身极其简单，其**唯一**职责是根据传入的`validation_type`，从一个插件注册表中查找并调用对应的插件。
    2.  **插件 (Validators)**: 所有具体的、专业的验证逻辑，都封装在独立的、实现了统一接口的插件中（如`StateMachineValidator`, `BoundaryConditionValidator`）。
    3.  **AI的职责转变**: AI在生成验证契约时，其任务从“生成复杂的验证逻辑”转变为“**为专业插件准备精确的、结构化的参数**”，极大地降低了AI任务的复杂度，提升了输出的可靠性。

### 2. “统一模型承载万物”模式 (Unified Model for All)
这是V4.2数据模型设计的核心，是系统可扩展性的基石。
- **问题**: 如何在不频繁修改核心数据结构的前提下，支持未来不断出现的新“关键设计要素”？
- **V4.2解决方案**:
    - **一个模型 (`AtomicConstraint`)**: 我们不为每个新概念创建一个新模型，而是让所有概念共享一个统一的结构。
    - **两个“插槽” (`category` 和 `params`)**:
        - `category`字段作为“**语义分类器**”，告诉系统“这是什么”。
        - `params`字段作为“**万能货箱**”，用于承载该`category`下所有独特的、结构化的数据。
- **原理**: 这种模式将系统的“变”与“不变”完美分离。核心框架只关心`AtomicConstraint`这个“不变”的结构，而所有“变”的业务逻辑，则由理解特定`category`的插件来处理。

### 3. 分层角色特化模式 (Layered Role Specialization)
*(与V4.1基本一致，职责微调)*
- **`首席架构师AI` (决策层)**: 负责将`01号`文档中的设计思想，**分类**并**结构化**为带有精确`category`和`params`的`AtomicConstraint`对象。
- **`领域专家AI` (逻辑层)**: 负责**消费**全局知识库，并基于对`category`的理解，进行“引用”或“分叉”的决策。
- **`资深工程师AI` (执行层)**: 负责**消费**所有继承的`AtomicConstraint`，并生成最终的、符合所有知识单元定义的产出。

### 4. “服务-经理”多实例管理模式 (Service-Manager Multi-Instance Pattern)
这是V4.2增强版架构的核心，用于管理多个并行的项目治理流程。
- **问题**: 如何在支持并行处理多个项目的同时，确保每个项目的上下文和状态是完全隔离的？
- **V4.2.1解决方案**:
    1.  **项目经理服务 (`ProjectManagerService`)**: 作为一个应用级的**单例服务**，它充当所有项目治理任务的唯一入口和**工厂**。
    2.  **项目经理 (`ProjectManager`)**: 作为一个**实例类**，每个实例都与一个特定的项目路径绑定，并独立负责该项目的完整治理生命周期。
- **原理**: `ProjectManagerService` 接收所有请求，并根据项目路径分发给对应的 `ProjectManager` 实例。如果实例不存在，则创建一个新的。这种模式确保了不同项目之间的状态互不干扰，实现了真正的多实例并行处理。

## PM_V2 API实现模式

### 1. 渐进式API实现模式 (Progressive API Implementation)
这是PM_V2项目经理工作台API实现的核心模式，旨在平衡实现速度和架构完整性。

- **问题**: 如何在保证架构完整性的同时，快速实现核心功能？
- **解决方案**:
    1.  **第一阶段**: 实现核心目录管理API（立即实现）
    2.  **第二阶段**: 集成日志记录和状态监控（后续实现）
    3.  **第三阶段**: 完善WebSocket实时通信（后续实现）

### 2. 单一职责API模式 (Single Responsibility API Pattern)
重构API以遵循单一职责原则，将业务逻辑从路由处理程序中分离出来。

- **问题**: 如何使API更健壮、更易于维护，并确保业务逻辑位于正确的分层？
- **解决方案**:
    1.  **API端点简化**: 将多个相关操作（如验证、创建）合并到一个有意义的端点（`/get_and_create`）。
    2.  **业务逻辑迁移**: 将所有业务逻辑（包括验证）从Flask蓝图（视图层）移动到专门的服务层（`ProjectManagerService`）。
    3.  **蓝图作为调度器**: 蓝图（`pm_v2_blueprint.py`）现在只负责解析HTTP请求并调用服务层，不再包含任何业务逻辑。

### 3. 蓝图路由模式 (Blueprint Routing Pattern)
使用Flask蓝图模式组织PM_V2相关的API路由。

- **问题**: 如何组织和管理PM_V2相关的API端点？
- **解决方案**:
    1.  **蓝图注册**: 在`app.py`中注册`pm_v2_bp`蓝图
    2.  **路由前缀**: 使用`/api/pm_v2`前缀统一管理API
    3.  **模块化设计**: 将PM_V2相关功能封装在独立的蓝图中

### 4. 前端集成模式 (Frontend Integration Pattern)
实现前端组件与后端API的完整集成。

- **问题**: 如何实现前端目录输入弹窗与后端API的无缝集成？
- **解决方案**:
    1.  **事件驱动**: 使用事件监听器处理用户交互
    2.  **异步处理**: 使用async/await处理API调用
    3.  **状态管理**: 实现加载状态、错误状态的状态管理
    4.  **用户体验**: 提供实时的用户反馈和错误提示

### 5. 服务层驱动流程模式 (Service-Layer Driven Flow)
业务流程由服务层统一协调，而不是由API端点驱动。

- **问题**: 如何确保业务流程的原子性和一致性？
- **解决方案**:
    1.  **统一入口**: `ProjectManagerService`的`get_or_create_manager`方法成为创建流程的唯一入口。
    2.  **内置验证**: 目录验证作为业务流程的第一步，内置于服务方法中，确保在执行核心逻辑前所有前置条件都已满足。
    3.  **原子操作**: 从API的角度来看，这是一个原子操作，要么成功创建/获取项目经理，要么因验证失败而返回错误。
