/**
 * 统一HTTP客户端 - 处理所有API请求
 */
class HttpClient {
    constructor(baseURL = 'http://localhost:25526/api/v2') {
        this.baseURL = baseURL;
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
        this.requestInterceptors = [];
        this.responseInterceptors = [];
    }
    
    /**
     * 添加请求拦截器
     * @param {function} interceptor - 拦截器函数
     */
    addRequestInterceptor(interceptor) {
        this.requestInterceptors.push(interceptor);
    }
    
    /**
     * 添加响应拦截器
     * @param {function} interceptor - 拦截器函数
     */
    addResponseInterceptor(interceptor) {
        this.responseInterceptors.push(interceptor);
    }
    
    /**
     * 通用请求方法
     * @param {string} method - HTTP方法
     * @param {string} url - 请求URL
     * @param {object} data - 请求数据
     * @param {object} headers - 请求头
     * @param {object} options - 其他选项
     * @returns {Promise} 响应Promise
     */
    async request(method, url, data = null, headers = {}, options = {}) {
        const fullURL = url.startsWith('http') ? url : `${this.baseURL}${url}`;
        
        let config = {
            method,
            headers: { ...this.defaultHeaders, ...headers },
            ...options
        };
        
        // 处理请求数据
        if (data) {
            if (method === 'GET') {
                // GET请求将数据转换为查询参数
                const queryString = new URLSearchParams(data).toString();
                const separator = fullURL.includes('?') ? '&' : '?';
                config.url = queryString ? `${fullURL}${separator}${queryString}` : fullURL;
            } else {
                // 其他请求将数据放在body中
                config.body = JSON.stringify(data);
            }
        }
        
        // 应用请求拦截器
        for (const interceptor of this.requestInterceptors) {
            config = await interceptor(config);
        }
        
        const requestURL = config.url || fullURL;
        
        try {
            console.log(`HTTP ${method} ${requestURL}`);
            const startTime = Date.now();
            
            const response = await fetch(requestURL, config);
            const endTime = Date.now();
            
            console.log(`HTTP ${method} ${requestURL} - ${response.status} (${endTime - startTime}ms)`);
            
            let responseData;
            const contentType = response.headers.get('content-type');
            
            if (contentType && contentType.includes('application/json')) {
                responseData = await response.json();
            } else {
                responseData = await response.text();
            }
            
            // 应用响应拦截器
            for (const interceptor of this.responseInterceptors) {
                responseData = await interceptor(responseData, response);
            }
            
            if (!response.ok) {
                const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
                error.status = response.status;
                error.response = responseData;
                throw error;
            }
            
            return responseData;
        } catch (error) {
            console.error(`HTTP request failed: ${method} ${requestURL}`, error);
            
            // 处理网络错误
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                error.message = '网络连接失败，请检查网络连接或服务器状态';
            }
            
            throw error;
        }
    }
    
    /**
     * GET请求
     * @param {string} url - 请求URL
     * @param {object} params - 查询参数
     * @param {object} headers - 请求头
     * @returns {Promise} 响应Promise
     */
    get(url, params = {}, headers = {}) {
        return this.request('GET', url, params, headers);
    }
    
    /**
     * POST请求
     * @param {string} url - 请求URL
     * @param {object} data - 请求数据
     * @param {object} headers - 请求头
     * @returns {Promise} 响应Promise
     */
    post(url, data = {}, headers = {}) {
        return this.request('POST', url, data, headers);
    }
    
    /**
     * PUT请求
     * @param {string} url - 请求URL
     * @param {object} data - 请求数据
     * @param {object} headers - 请求头
     * @returns {Promise} 响应Promise
     */
    put(url, data = {}, headers = {}) {
        return this.request('PUT', url, data, headers);
    }
    
    /**
     * DELETE请求
     * @param {string} url - 请求URL
     * @param {object} headers - 请求头
     * @returns {Promise} 响应Promise
     */
    delete(url, headers = {}) {
        return this.request('DELETE', url, null, headers);
    }
    
    /**
     * 文件上传
     * @param {string} url - 上传URL
     * @param {FormData} formData - 表单数据
     * @param {function} onProgress - 进度回调
     * @returns {Promise} 响应Promise
     */
    upload(url, formData, onProgress = null) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            
            // 设置进度监听
            if (onProgress) {
                xhr.upload.addEventListener('progress', (event) => {
                    if (event.lengthComputable) {
                        const percentComplete = (event.loaded / event.total) * 100;
                        onProgress(percentComplete);
                    }
                });
            }
            
            xhr.addEventListener('load', () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (error) {
                        resolve(xhr.responseText);
                    }
                } else {
                    reject(new Error(`Upload failed: ${xhr.status} ${xhr.statusText}`));
                }
            });
            
            xhr.addEventListener('error', () => {
                reject(new Error('Upload failed: Network error'));
            });
            
            const fullURL = url.startsWith('http') ? url : `${this.baseURL}${url}`;
            xhr.open('POST', fullURL);
            xhr.send(formData);
        });
    }
    
    /**
     * 文件下载
     * @param {string} url - 下载URL
     * @param {string} filename - 文件名
     * @returns {Promise} 下载Promise
     */
    async download(url, filename = null) {
        try {
            const response = await fetch(url.startsWith('http') ? url : `${this.baseURL}${url}`);
            
            if (!response.ok) {
                throw new Error(`Download failed: ${response.status} ${response.statusText}`);
            }
            
            const blob = await response.blob();
            
            // 创建下载链接
            const downloadURL = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadURL;
            
            // 设置文件名
            if (filename) {
                link.download = filename;
            } else {
                // 尝试从响应头获取文件名
                const contentDisposition = response.headers.get('content-disposition');
                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                    if (filenameMatch) {
                        link.download = filenameMatch[1];
                    }
                }
            }
            
            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 清理URL对象
            window.URL.revokeObjectURL(downloadURL);
            
            return true;
        } catch (error) {
            console.error('Download failed:', error);
            throw error;
        }
    }
    
    /**
     * 批量请求
     * @param {Array} requests - 请求配置数组
     * @returns {Promise} 批量响应Promise
     */
    async batch(requests) {
        const promises = requests.map(req => {
            const { method = 'GET', url, data, headers } = req;
            return this.request(method, url, data, headers).catch(error => ({
                error: error.message,
                request: req
            }));
        });
        
        return Promise.all(promises);
    }
    
    /**
     * 重试请求
     * @param {function} requestFn - 请求函数
     * @param {number} maxRetries - 最大重试次数
     * @param {number} delay - 重试延迟(毫秒)
     * @returns {Promise} 响应Promise
     */
    async retry(requestFn, maxRetries = 3, delay = 1000) {
        let lastError;
        
        for (let i = 0; i <= maxRetries; i++) {
            try {
                return await requestFn();
            } catch (error) {
                lastError = error;
                
                if (i < maxRetries) {
                    console.log(`Request failed, retrying in ${delay}ms... (${i + 1}/${maxRetries})`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                    delay *= 2; // 指数退避
                }
            }
        }
        
        throw lastError;
    }
    
    /**
     * 设置认证令牌
     * @param {string} token - 认证令牌
     */
    setAuthToken(token) {
        if (token) {
            this.defaultHeaders['Authorization'] = `Bearer ${token}`;
        } else {
            delete this.defaultHeaders['Authorization'];
        }
    }
    
    /**
     * 设置基础URL
     * @param {string} baseURL - 基础URL
     */
    setBaseURL(baseURL) {
        this.baseURL = baseURL;
    }
    
    /**
     * 获取当前配置
     * @returns {object} 当前配置
     */
    getConfig() {
        return {
            baseURL: this.baseURL,
            defaultHeaders: { ...this.defaultHeaders }
        };
    }
}

// 创建默认实例
const httpClient = new HttpClient();

// 添加默认的响应拦截器
httpClient.addResponseInterceptor((data, response) => {
    // 如果响应包含错误信息，记录日志
    if (data && data.error) {
        console.warn('API returned error:', data.error);
    }
    
    return data;
});

// 添加默认的请求拦截器
httpClient.addRequestInterceptor((config) => {
    // 添加时间戳防止缓存
    if (config.method === 'GET') {
        const url = new URL(config.url || config.path);
        url.searchParams.set('_t', Date.now());
        config.url = url.toString();
    }
    
    return config;
});

// 导出默认实例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HttpClient;
} else {
    window.HttpClient = HttpClient;
    window.httpClient = httpClient;
}
